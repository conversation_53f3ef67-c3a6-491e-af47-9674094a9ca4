<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Robinhood Crypto Bot Dashboard</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Robinhood Crypto Bot</h1>
      <p>Automated Trading Dashboard</p>
      <!-- Clean Symbol Switcher -->
      <div class="clean-symbol-switcher">
        <div class="symbol-buttons">
          <button class="symbol-btn active" data-symbol="BTC-USD">
            <span class="btn-icon">₿</span>
            <span class="btn-label">BTC</span>
            <span class="btn-price" id="btc-price">$0.00</span>
          </button>
          <button class="symbol-btn" data-symbol="ETH-USD">
            <span class="btn-icon">Ξ</span>
            <span class="btn-label">ETH</span>
            <span class="btn-price" id="eth-price">$0.00</span>
          </button>
          <button class="symbol-btn" data-symbol="DOGE-USD">
            <span class="btn-icon">Ð</span>
            <span class="btn-label">DOGE</span>
            <span class="btn-price" id="doge-price">$0.00</span>
          </button>
          <button class="symbol-btn" data-symbol="LTC-USD">
            <span class="btn-icon">Ł</span>
            <span class="btn-label">LTC</span>
            <span class="btn-price" id="ltc-price">$0.00</span>
          </button>
          <button class="symbol-btn" data-symbol="BCH-USD">
            <span class="btn-icon">₿</span>
            <span class="btn-label">BCH</span>
            <span class="btn-price" id="bch-price">$0.00</span>
          </button>
        </div>
      </div>

      <!-- Hidden select for backend compatibility -->
      <select id="symbolSelect" style="display: none;">
        <option value="BTC-USD">Bitcoin (BTC-USD)</option>
        <option value="ETH-USD">Ethereum (ETH-USD)</option>
        <option value="DOGE-USD">Dogecoin (DOGE-USD)</option>
        <option value="LTC-USD">Litecoin (LTC-USD)</option>
        <option value="BCH-USD">Bitcoin Cash (BCH-USD)</option>
      </select>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <h3>Trading Status</h3>
        <div class="stat-value" id="tradingStatus">🤖 Active</div>
        <div class="stat-subtitle" id="tradingFunds">$0.00 available</div>
      </div>
      
      <div class="stat-card">
        <h3>Total Trades</h3>
        <div class="stat-value" id="trades">0</div>
      </div>
      
      <div class="stat-card">
        <h3>Account Balance</h3>
        <div class="stat-value" id="balance">$0.00</div>
      </div>
      
      <!-- Multi-Currency Holdings Cards -->
      <div class="stat-card holdings-card" id="btc-holdings-card">
        <h3>₿ BTC Holdings</h3>
        <div class="stat-value" id="btc-holdings">0.********</div>
        <div class="stat-subtitle" id="btc-value">$0.00</div>
      </div>

      <div class="stat-card holdings-card" id="eth-holdings-card" style="display: none;">
        <h3>Ξ ETH Holdings</h3>
        <div class="stat-value" id="eth-holdings">0.********</div>
        <div class="stat-subtitle" id="eth-value">$0.00</div>
      </div>

      <div class="stat-card holdings-card" id="doge-holdings-card" style="display: none;">
        <h3>Ð DOGE Holdings</h3>
        <div class="stat-value" id="doge-holdings">0.********</div>
        <div class="stat-subtitle" id="doge-value">$0.00</div>
      </div>

      <div class="stat-card holdings-card" id="ltc-holdings-card" style="display: none;">
        <h3>Ł LTC Holdings</h3>
        <div class="stat-value" id="ltc-holdings">0.********</div>
        <div class="stat-subtitle" id="ltc-value">$0.00</div>
      </div>

      <div class="stat-card holdings-card" id="bch-holdings-card" style="display: none;">
        <h3>₿ BCH Holdings</h3>
        <div class="stat-value" id="bch-holdings">0.********</div>
        <div class="stat-subtitle" id="bch-value">$0.00</div>
      </div>

      <div class="stat-card">
        <h3 id="priceTitle">
          <span id="priceIcon">₿</span>
          <span id="priceLabel">Current BTC Price</span>
        </h3>
        <div class="stat-value" id="cryptoPrice">$0.00</div>
        <div class="stat-subtitle" id="priceChange">+0.00%</div>
      </div>
      
      <div class="stat-card">
        <h3>Portfolio Value</h3>
        <div class="stat-value" id="portfolioValue">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>P&L Today</h3>
        <div class="stat-value" id="pnl">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>Stop Losses</h3>
        <div class="stat-value" id="stopLoss">None</div>
        <div class="stop-loss-details" id="stopLossDetails"></div>
      </div>
    </div>

    <!-- Portfolio Balance Chart -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>Portfolio Balance</h3>
          <div class="balance-summary">
            <span class="balance-item">Cash: <span id="cashBalance">$0.00</span></span>
            <span class="balance-item">Holdings: <span id="holdingsValue">$0.00</span></span>
            <span class="balance-item">Total: <span id="totalValue">$0.00</span></span>
            <span class="balance-item pnl" id="pnlDisplay">P&L: $0.00</span>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="portfolioChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BTC Price Chart -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3 id="chartTitle">BTC Price Chart (OHLC)</h3>
          <div class="chart-controls">
            <div class="timeframe-selector">
              <label>Timeframe:</label>
              <button class="timeframe-btn active" data-timeframe="1m">1m</button>
              <button class="timeframe-btn" data-timeframe="5m">5m</button>
              <button class="timeframe-btn" data-timeframe="15m">15m</button>
              <button class="timeframe-btn" data-timeframe="1h">1h</button>
              <button class="timeframe-btn" data-timeframe="4h">4h</button>
            </div>
            <div class="chart-period-selector">
              <label>Period:</label>
              <button class="period-btn" data-period="1h">1H</button>
              <button class="period-btn" data-period="6h">6H</button>
              <button class="period-btn active" data-period="1d">1D</button>
              <button class="period-btn" data-period="3d">3D</button>
              <button class="period-btn" data-period="1w">1W</button>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="priceChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="controls">
      <div class="balance-control">
        <label for="maxBalanceSlider">Max Trading Balance: $<span id="maxBalanceValue">0</span></label>
        <input type="range" id="maxBalanceSlider" min="0" max="1000" value="0" step="10" class="balance-slider">
        <div class="balance-info">
          <span class="balance-min">$0</span>
          <span class="balance-max">Available: $<span id="availableBalance">0</span></span>
        </div>
      </div>

      <div class="balance-control">
        <label for="rebalanceSlider">Rebalance Frequency: <span id="rebalanceValue">24</span> hours</label>
        <input type="range" id="rebalanceSlider" min="1" max="168" value="24" step="1" class="balance-slider">
        <div class="balance-info">
          <span class="balance-min">1h</span>
          <span class="balance-max">1 week (168h)</span>
        </div>
      </div>

      <div class="symbol-allocations">
        <h3>Symbol Allocations</h3>
        <div class="allocation-info">
          <span class="allocation-total">Total: <span id="allocationTotal">100</span>%</span>
          <span class="allocation-status" id="allocationStatus">✅ Balanced</span>
        </div>
        <div id="symbolAllocationSliders" class="allocation-sliders">
          <!-- Symbol allocation sliders will be dynamically generated here -->
        </div>
      </div>
    </div>

    <div class="strategy-section">
      <div class="strategy-card">
        <h3>Portfolio Strategy Management</h3>
        <div class="portfolio-overview">
          <div class="portfolio-stats">
            <div class="stat-item">
              <label>Active Strategies:</label>
              <span id="activeStrategies">--</span>
            </div>
            <div class="stat-item">
              <label>Last Rebalance:</label>
              <span id="lastRebalance">--</span>
            </div>
            <div class="stat-item">
              <label>Rebalance Frequency:</label>
              <span id="rebalanceFrequency">--</span>
            </div>
            <div class="stat-item">
              <label>Next Rebalance:</label>
              <span id="nextRebalance">--</span>
            </div>
          </div>
        </div>
        
        <div class="strategies-grid" id="strategiesGrid">
          <!-- Individual strategy cards will be populated here -->
        </div>
      </div>
    </div>

    <div class="logs-section">
      <div class="logs-card">
        <h3>Trading Activity</h3>
        <div id="log" class="log-container"></div>
      </div>
    </div>
  </div>

  <!-- Strategy Configuration Modal -->
  <div id="strategyConfigModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="configModalTitle">Configure Strategy</h3>
        <button class="modal-close" onclick="closeStrategyConfig()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="configModalDescription" class="config-description"></div>
        <form id="strategyConfigForm">
          <div id="configFields" class="config-fields">
            <!-- Dynamic config fields will be inserted here -->
          </div>
          <div class="config-actions">
            <button type="button" class="btn btn-secondary" onclick="closeStrategyConfig()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="resetStrategyConfig()">Reset to Defaults</button>
            <button type="submit" class="btn btn-success">Save Configuration</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Generic Alert Modal -->
  <div id="alertModal" class="modal">
    <div class="modal-content alert-modal">
      <div class="modal-header">
        <h3 id="alertModalTitle">Alert</h3>
        <button class="modal-close" onclick="closeAlertModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="alertModalMessage" class="alert-message"></div>
        <div class="modal-actions">
          <button type="button" class="btn btn-primary" onclick="closeAlertModal()">OK</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Generic Confirm Modal -->
  <div id="confirmModal" class="modal">
    <div class="modal-content confirm-modal">
      <div class="modal-header">
        <h3 id="confirmModalTitle">Confirm</h3>
        <button class="modal-close" onclick="closeConfirmModal(false)">&times;</button>
      </div>
      <div class="modal-body">
        <div id="confirmModalMessage" class="confirm-message"></div>
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" onclick="closeConfirmModal(false)">Cancel</button>
          <button type="button" class="btn btn-primary" onclick="closeConfirmModal(true)">OK</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Position Details Modal -->
  <div id="positionModal" class="modal">
    <div class="modal-content modal-large">
      <div class="modal-header">
        <h3 id="positionModalTitle">Strategy Positions</h3>
        <button class="modal-close" onclick="closePositionModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="positionModalContent" class="position-details">
          <!-- Position details will be populated here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Trade History Modal -->
  <div id="historyModal" class="modal">
    <div class="modal-content modal-large">
      <div class="modal-header">
        <h3 id="historyModalTitle">Trade History</h3>
        <button class="modal-close" onclick="closeHistoryModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="historyModalContent" class="history-details">
          <!-- Trade history will be populated here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Sticky Market Ticker at Bottom -->
  <div class="sticky-market-ticker">
    <div class="market-ticker-scroll" id="stickyMarketTicker">
      <span class="ticker-item">BTC: $0.00 (+0.00%)</span>
      <span class="ticker-item">ETH: $0.00 (+0.00%)</span>
      <span class="ticker-item">DOGE: $0.00 (+0.00%)</span>
      <span class="ticker-item">LTC: $0.00 (+0.00%)</span>
      <span class="ticker-item">BCH: $0.00 (+0.00%)</span>
      <span class="ticker-item">XRP: $0.00 (+0.00%)</span>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial/dist/chartjs-chart-financial.min.js"></script>
  <script src="script.js?v=43"></script>
</body>
</html>
