* { 
  margin: 0; 
  padding: 0; 
  box-sizing: border-box; 
}

body { 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: #000000;
  color: #ffffff;
  min-height: 100vh;
  line-height: 1.4;
  font-size: 16px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px 0;
}

.header h1 {
  font-size: 32px;
  font-weight: 300;
  margin-bottom: 8px;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.header p {
  color: #9ca3af;
  font-size: 16px;
  font-weight: 400;
}

/* Clean Symbol Switcher */
.clean-symbol-switcher {
  margin: 20px 0;
  text-align: center;
}

.symbol-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.symbol-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  color: #9ca3af;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
  position: relative;
}

.symbol-btn:hover {
  background: #2a2a2a;
  border-color: #3a3a3a;
  color: #ffffff;
  transform: translateY(-2px);
}

.symbol-btn.active {
  background: #00C805;
  border-color: #00C805;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 200, 5, 0.3);
}

.btn-icon {
  font-size: 20px;
  font-weight: bold;
}

.btn-label {
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.btn-price {
  font-size: 10px;
  font-weight: 500;
  opacity: 0.8;
  margin-top: 2px;
}

/* Holdings Cards */
.holdings-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #3a3a3a;
}

.holdings-card h3 {
  color: #00C805;
  font-size: 12px;
}

.holdings-card .stat-value {
  font-size: 16px;
  font-weight: 600;
}

.holdings-card .stat-subtitle {
  color: #9ca3af;
  font-size: 11px;
}

/* Sticky Market Ticker */
.sticky-market-ticker {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #1a1a1a;
  border-top: 1px solid #2a2a2a;
  padding: 8px 0;
  z-index: 1000;
  overflow: hidden;
}

.market-ticker-scroll {
  display: flex;
  gap: 30px;
  animation: scroll-ticker-bottom 45s linear infinite;
  white-space: nowrap;
}

.sticky-market-ticker .ticker-item {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
  padding: 0 15px;
}

@keyframes scroll-ticker-bottom {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* Add bottom padding to body to account for sticky ticker */
body {
  padding-bottom: 40px;
}

/* Price change colors */
.positive {
  color: #00C805 !important;
}

.negative {
  color: #ef4444 !important;
}

.ticker-item.positive {
  color: #00C805;
}

.ticker-item.negative {
  color: #ef4444;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.stat-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  transition: border-color 0.2s ease;
}

.stat-card:hover {
  border-color: #3a3a3a;
}

.stat-card h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 400;
  color: #ffffff;
  line-height: 1.2;
}

.stat-subtitle {
  font-size: 14px;
  font-weight: 300;
  color: #b0b0b0;
  margin-top: 4px;
  line-height: 1.2;
}

.status-running { 
  color: #00C805; 
  font-weight: 500;
}

.status-stopped { 
  color: #FF6B35; 
  font-weight: 500;
}

.positive { 
  color: #00C805; 
}

.negative {
  color: #FF6B35;
}

/* Bot toggle styles removed - bot now auto-starts and always runs */

.stop-loss-details {
  margin-top: 8px;
  font-size: 12px;
  color: #9ca3af;
}

.stop-loss-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #2a2a2a;
}

.stop-loss-item:last-child {
  border-bottom: none;
}

.stop-loss-strategy {
  font-weight: 500;
  color: #e5e7eb;
}

.stop-loss-price {
  color: #10b981;
  font-weight: 600;
}

.trading-metrics {
  margin-top: 16px;
  padding: 12px;
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 6px;
}

.trading-metrics h5 {
  margin: 0 0 12px 0;
  color: #10b981;
  font-size: 14px;
  font-weight: 600;
}

.position-info, .stop-loss-info, .pending-orders-info {
  margin-bottom: 8px;
}

.position-info:last-child, .stop-loss-info:last-child, .pending-orders-info:last-child {
  margin-bottom: 0;
}

.trading-metrics .stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.trading-metrics .stat-row span:first-child {
  color: #9ca3af;
  font-weight: 500;
}

.trading-metrics .stat-row span:last-child {
  color: #e5e7eb;
  font-weight: 600;
}

.trading-metrics .positive {
  color: #10b981;
}

.trading-metrics .negative {
  color: #ef4444;
}

.trading-metrics .stop-loss-price {
  color: #f59e0b;
  font-weight: 700;
}

.pending-count {
  color: #3b82f6;
  font-weight: 600;
}

.pending-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 8px;
  margin: 2px 0;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
  font-size: 11px;
}

.order-type {
  color: #3b82f6;
  font-weight: 600;
  text-transform: uppercase;
}

.order-details {
  color: #9ca3af;
  font-family: monospace;
}

.controls {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toggle-btn {
  background: linear-gradient(45deg, #10b981, #059669);
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.portfolio-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.rebalance-btn {
  background: linear-gradient(45deg, #f59e0b, #d97706);
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rebalance-btn:hover {
  background: linear-gradient(45deg, #d97706, #b45309);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.rebalance-btn:active {
  transform: translateY(0);
}

.balance-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.balance-control label {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 400;
}

.balance-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #374151;
  outline: none;
  cursor: pointer;
}

.balance-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.balance-min, .balance-max {
  font-weight: 400;
}

.strategy-section {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.strategy-section h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 16px;
}

.strategy-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.strategy-selector select {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  min-width: 200px;
  cursor: pointer;
}

.strategy-selector select:focus {
  outline: none;
  border-color: #10b981;
}

.apply-btn {
  background: #00C805;
  color: #ffffff;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.apply-btn:hover {
  background: #00b004;
}

.strategy-config {
  border-top: 1px solid #2a2a2a;
  padding-top: 20px;
  margin-bottom: 20px;
}

.strategy-config h4 {
  margin-bottom: 16px;
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-item input {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  font-size: 14px;
}

.config-item input:focus {
  outline: none;
  border-color: #10b981;
}

.logs-section {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
}

.logs-section h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 16px;
}

#log {
  background: #0f0f0f;
  border: 1px solid #2a2a2a;
  border-radius: 4px;
  padding: 12px;
  height: 200px;
  overflow-y: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 2px;
  opacity: 0.7;
  font-size: 13px;
}

.log-entry:last-child {
  opacity: 1;
  color: #ffffff;
  font-weight: 500;
}

.chart-section {
  margin-bottom: 24px;
}

.chart-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 15px;
}

.chart-header h3 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.balance-summary {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.balance-item {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
}

.balance-item.pnl {
  font-weight: 600;
}

.balance-item.pnl.positive {
  color: #10b981;
}

.balance-item.pnl.negative {
  color: #ef4444;
}

.chart-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

/* Gap filling buttons removed - process is now automatic */

.chart-controls label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.timeframe-selector,
.chart-period-selector {
  display: flex;
  gap: 4px;
  align-items: center;
}

.chart-timeframe {
  display: flex;
  gap: 4px;
}

.timeframe-btn,
.period-btn {
  background: transparent;
  border: 1px solid #374151;
  color: #9ca3af;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  font-weight: 500;
}

.timeframe-btn:hover,
.period-btn:hover {
  border-color: #4b5563;
  color: #f9fafb;
}

.timeframe-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.period-btn.active {
  background: #10b981;
  border-color: #10b981;
  color: #ffffff;
}

.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

.chart-tooltip {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #2a2a2a;
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
  padding: 8px 12px;
  pointer-events: none;
  position: absolute;
  z-index: 100;
}

.chart-tooltip-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.chart-tooltip-value {
  color: #00C805;
}

.chart-tooltip-date {
  color: #9ca3af;
  font-size: 11px;
  margin-top: 4px;
}

.chart-event-marker {
  cursor: pointer;
}

/* Forming candle indicator */
.forming-candle-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #3b82f6;
  font-weight: 500;
  display: none;
}

.forming-candle-indicator.active {
  display: block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.strategy-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.mode-toggle {
  display: flex;
  background: #374151;
  border-radius: 6px;
  padding: 4px;
}

.mode-btn {
  background: transparent;
  border: none;
  color: #9ca3af;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.mode-btn.active {
  background: #10b981;
  color: white;
}

.mode-section {
  transition: opacity 0.3s ease;
}

.single-strategy-selector {
  background: #1f2937;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.strategy-select {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  width: 200px;
  margin-right: 12px;
}

.single-config {
  background: #1f2937;
  border-radius: 8px;
  padding: 20px;
  display: none;
}

.strategy-control-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.strategy-control-card.active {
  border-color: #10b981;
  background: #1a2e1a;
}

.strategy-control-card.inactive {
  opacity: 0.7;
}

.strategy-control-card.underfunded {
  border-color: #dc2626;
  background: linear-gradient(135deg, #1f2937 0%, #2d1b1b 100%);
}

.strategy-control-card.underfunded .strategy-header {
  border-bottom-color: #dc2626;
}

.strategy-control-card.underfunded .allocation-badge {
  background: #dc2626;
  color: #ffffff;
}

.strategy-control-card.underfunded .strategy-status {
  border-left-color: #dc2626;
  background: #2d1b1b;
}

/* Multi-currency position styling */
.multi-currency-summary {
  border: 1px solid #4b5563;
  border-radius: 6px;
  padding: 12px;
  background: #111827;
  margin-bottom: 8px;
}

.multi-currency-summary .stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 0.9em;
}

.multi-currency-summary .stat-row:last-child {
  margin-bottom: 0;
  font-weight: 600;
  border-top: 1px solid #374151;
  padding-top: 6px;
  margin-top: 6px;
}

.single-currency-position {
  /* Keep existing styling for single currency positions */
}

/* Strategy highlight animation for trading decisions */
.strategy-control-card.strategy-highlight {
  animation: strategyBlink 0.6s ease-in-out infinite alternate;
  border-width: 2px;
  z-index: 10;
  position: relative;
}

@keyframes strategyBlink {
  0% {
    border-color: #10b981;
    background: linear-gradient(135deg, #1a2e1a 0%, #1a1a1a 100%);
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.4);
  }
  100% {
    border-color: #34d399;
    background: linear-gradient(135deg, #1e3a1e 0%, #1a2e1a 100%);
    box-shadow: 0 0 25px rgba(52, 211, 153, 0.7);
  }
}

/* Optional notification animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.underfunded-warning {
  background: #2d1b1b;
  border: 1px solid #dc2626;
  border-radius: 4px;
  padding: 12px;
  margin: 12px 0;
}

.underfunded-warning h5 {
  color: #fca5a5;
  font-size: 13px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.underfunded-warning h5::before {
  content: "⚠️";
  font-size: 14px;
}

.underfunded-warning p {
  color: #d1d5db;
  font-size: 12px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.underfunded-warning .suggestion {
  color: #9ca3af;
  font-size: 11px;
  font-style: italic;
  margin: 0;
}

/* Strategy Configuration Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #374151;
}

.modal-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #374151;
  color: #ffffff;
}

.modal-body {
  padding: 24px;
}

.config-description {
  color: #9ca3af;
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

.config-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-field label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.config-field .field-description {
  color: #9ca3af;
  font-size: 12px;
  margin-top: 2px;
}

.config-field input,
.config-field select {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.config-field input:focus,
.config-field select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.config-field input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.config-field .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #374151;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.btn-secondary {
  background: #374151;
  color: #9ca3af;
  border-color: #4b5563;
}

.btn-secondary:hover {
  background: #4b5563;
  color: #ffffff;
}

.btn-primary {
  background: #3b82f6;
  color: #ffffff;
  border-color: #2563eb;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-success {
  background: #10b981;
  color: #ffffff;
  border-color: #059669;
}

.btn-success:hover {
  background: #059669;
}

/* Alert and Confirm Modal Styles */
.alert-modal, .confirm-modal {
  max-width: 500px;
  width: 90%;
}

.alert-message, .confirm-message {
  color: #e5e7eb;
  line-height: 1.6;
  margin-bottom: 24px;
  white-space: pre-line;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.modal-actions .btn {
  min-width: 80px;
}

/* Success/Error message styling */
.alert-message.success {
  color: #10b981;
}

.alert-message.error {
  color: #ef4444;
}

.alert-message.warning {
  color: #f59e0b;
}

/* Icon support for messages */
.alert-message.success::before {
  content: "✅ ";
}

.alert-message.error::before {
  content: "❌ ";
}

.alert-message.warning::before {
  content: "⚠️ ";
}

/* Symbol Allocation Sliders */
.symbol-allocations {
  margin-top: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.symbol-allocations h3 {
  margin: 0 0 16px 0;
  color: #e5e7eb;
  font-size: 16px;
  font-weight: 600;
}

.allocation-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 14px;
}

.allocation-total {
  color: #e5e7eb;
  font-weight: 500;
}

.allocation-status {
  font-weight: 600;
}

.allocation-status.balanced {
  color: #10b981;
}

.allocation-status.unbalanced {
  color: #f59e0b;
}

.allocation-status.error {
  color: #ef4444;
}

.allocation-sliders {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.allocation-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.allocation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.allocation-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e5e7eb;
  font-size: 14px;
  font-weight: 500;
}

.symbol-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.allocation-value {
  color: #10b981;
  font-weight: 600;
  min-width: 50px;
  text-align: right;
}

.allocation-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.allocation-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #1f2937;
  transition: all 0.2s ease;
}

.allocation-slider::-webkit-slider-thumb:hover {
  background: #059669;
  transform: scale(1.1);
}

.allocation-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #1f2937;
  transition: all 0.2s ease;
}

.allocation-slider::-moz-range-thumb:hover {
  background: #059669;
  transform: scale(1.1);
}

.allocation-slider:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

.allocation-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

/* Responsive modal */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
    max-height: 90vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .config-actions {
    flex-direction: column;
    gap: 8px;
  }

  .config-actions .btn {
    width: 100%;
  }
}

/* Config field improvements */
.config-field input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.config-field input[type="number"]::-webkit-outer-spin-button,
.config-field input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.config-field input:invalid {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.config-field .field-description {
  font-style: italic;
}

.strategy-control-card:hover {
  border-color: #3a3a3a;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.strategy-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.strategy-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-btn {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  color: #9ca3af;
  cursor: pointer;
  padding: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.config-btn:hover {
  background: #4b5563;
  color: #ffffff;
  border-color: #6b7280;
}

.config-btn:active {
  transform: scale(0.95);
}

.strategy-control-card.underfunded .config-btn {
  background: #7f1d1d;
  border-color: #dc2626;
  color: #fca5a5;
}

.strategy-control-card.underfunded .config-btn:hover {
  background: #991b1b;
  color: #ffffff;
}

.history-btn {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  color: #9ca3af;
  cursor: pointer;
  padding: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-btn:hover {
  background: #4b5563;
  color: #ffffff;
  border-color: #6b7280;
}

.history-btn:active {
  transform: scale(0.95);
}

.strategy-control-card.underfunded .history-btn {
  background: #7f1d1d;
  border-color: #dc2626;
  color: #fca5a5;
}

.strategy-control-card.underfunded .history-btn:hover {
  background: #991b1b;
  color: #ffffff;
}

.strategy-title h4 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.strategy-toggle {
  margin-left: 16px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #374151;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #10b981;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.allocation-badge {
  background: #10b981;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.strategy-description {
  margin-bottom: 16px;
}

.strategy-description p {
  color: #9ca3af;
  font-size: 14px;
  margin: 0;
}

.strategy-status {
  margin-bottom: 16px;
  padding: 12px;
  background: #0f0f0f;
  border-radius: 4px;
  border-left: 3px solid #10b981;
}

.status-section h5 {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  gap: 2px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-value {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-value.positive {
  color: #10b981;
}

.status-value.neutral {
  color: #f59e0b;
}

.status-reason,
.status-next,
.status-trade {
  color: #e5e7eb;
  font-size: 12px;
  line-height: 1.4;
  font-style: italic;
}

.strategy-stats {
  border-top: 1px solid #2a2a2a;
  padding-top: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row span:first-child {
  color: #9ca3af;
  font-size: 13px;
}

.stat-row span:last-child {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
}

.strategy-inactive {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

.portfolio-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #0f0f0f;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .strategy-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .strategy-selector select {
    min-width: auto;
  }
  
  .header h1 {
    font-size: 28px;
  }
}

/* Position Details Modal - Invoice Style */
.modal-large {
  max-width: 900px;
  background: #ffffff;
  color: #1f2937;
  transform: scale(0.95);
  opacity: 0;
  transition: all 0.2s ease-out;
}

.modal.show .modal-large {
  transform: scale(1);
  opacity: 1;
}

.modal-large .modal-header {
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
  padding: 24px 32px;
}

.modal-large .modal-header h3 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.modal-large .modal-close {
  color: #6b7280;
  font-size: 28px;
  width: 36px;
  height: 36px;
}

.modal-large .modal-close:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.modal-large .modal-body {
  padding: 32px;
  background: #ffffff;
}

.position-details {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Live indicator for position modal */
.live-indicator {
  font-size: 12px;
  font-weight: 500;
  margin-left: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  transition: all 0.3s ease;
}

/* Price change animations */
.current-price, .current-value, .unrealized-pnl, .total-pnl {
  transition: color 0.3s ease;
}

.position-item {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.position-header {
  background: #f8fafc;
  padding: 20px 24px;
  margin: 0;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.position-header h4 {
  color: #1f2937;
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.position-symbol {
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  background: #e5e7eb;
  padding: 4px 12px;
  border-radius: 20px;
}

.position-details-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 40px;
  background: #ffffff;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
  min-height: 48px;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  color: #374151;
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  flex: 1;
}

.detail-item span {
  color: #1f2937;
  font-size: 16px;
  font-weight: 700;
  text-align: right;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  flex: 1;
}

.detail-item .positive {
  color: #059669;
  background: #d1fae5;
  padding: 4px 8px;
  border-radius: 6px;
}

.detail-item .negative {
  color: #dc2626;
  background: #fee2e2;
  padding: 4px 8px;
  border-radius: 6px;
}

/* Responsive design for position modal */
@media (max-width: 768px) {
  .modal-large {
    max-width: 95%;
    margin: 10px;
  }

  .modal-large .modal-header {
    padding: 20px 24px;
  }

  .modal-large .modal-header h3 {
    font-size: 20px;
  }

  .modal-large .modal-body {
    padding: 24px 20px;
  }

  .position-details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 20px;
  }

  .detail-item {
    padding: 10px 0;
    min-height: 40px;
  }

  .detail-item label {
    font-size: 14px;
  }

  .detail-item span {
    font-size: 15px;
  }
}

.no-positions {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.clickable-positions {
  cursor: pointer;
  color: #007bff;
  text-decoration: underline;
}

.clickable-positions:hover {
  color: #0056b3;
}

/* Symbol Status Display */
.symbol-statuses {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.symbol-status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

.symbol-code {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
}

.symbol-price {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

.symbol-decision {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.symbol-decision.ready {
  background: #10b981;
  color: white;
}

.symbol-decision.waiting {
  background: #6b7280;
  color: white;
}

/* Trade History Modal Styles */
.history-details {
  background: #ffffff;
  color: #1f2937;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.history-table th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
  font-size: 14px;
}

.history-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.history-table tr:hover {
  background: #f9fafb;
}

.history-table tr:last-child td {
  border-bottom: none;
}

.trade-type-buy {
  color: #059669;
  font-weight: 600;
  background: #d1fae5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.trade-type-sell {
  color: #dc2626;
  font-weight: 600;
  background: #fee2e2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.history-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.history-stat {
  text-align: center;
}

.history-stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.history-stat-value.positive {
  color: #059669;
}

.history-stat-value.negative {
  color: #dc2626;
}

.no-trades {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-style: italic;
}

/* Responsive design for history modal */
@media (max-width: 768px) {
  .history-table {
    font-size: 12px;
  }

  .history-table th,
  .history-table td {
    padding: 8px 12px;
  }

  .history-summary {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
  }

  .history-stat-value {
    font-size: 16px;
  }
}
