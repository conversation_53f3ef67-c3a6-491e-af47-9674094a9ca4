// Config validation and migration utility
import { createStrategy } from './strategies/index.js';
import fs from 'fs';
import path from 'path';

export class ConfigValidator {
  constructor() {
    this.validationErrors = [];
    this.fixesApplied = [];
    this.migrationLog = [];
  }

  // Get default configuration for a strategy
  getStrategyDefaults(strategyName) {
    try {
      const tempStrategy = createStrategy(strategyName, {});
      const schema = tempStrategy.getConfigSchema ? tempStrategy.getConfigSchema() : {};
      const defaults = {};
      
      // Extract default values from schema
      Object.entries(schema).forEach(([key, config]) => {
        if (config.default !== undefined) {
          defaults[key] = config.default;
        }
      });
      
      // Add strategy-specific required defaults
      const strategyDefaults = this.getStrategySpecificDefaults(strategyName);
      return { ...defaults, ...strategyDefaults };
    } catch (error) {
      console.warn(`Could not get defaults for strategy ${strategyName}:`, error.message);
      return {};
    }
  }

  // Strategy-specific defaults that might not be in schema
  getStrategySpecificDefaults(strategyName) {
    const defaults = {
      'trend-following': {
        minTrendLookback: 20,
        shortTermPeriod: 5,
        recentHighThreshold: 0.98,
        volatilityLookback: 14,
        timeframe: '5m'
      },
      'buy-the-dip': {
        minTrendLookback: 20,
        dipThreshold: 0.05,
        dipLookback: 5,
        timeframe: '5m'
      },
      'volatility-weighted': {
        minTrendLookback: 20,
        riskFactor: 0.02,
        timeframe: '5m'
      },
      'moving-average-crossover': {
        shortPeriod: 5,
        longPeriod: 20,
        timeframe: '5m'
      },
      'mean-reversion': {
        meanPeriod: 20,
        deviationThreshold: 0.04,
        timeframe: '5m'
      },
      'range-trading': {
        rangeLookback: 20,
        entryBufferPercent: 0.02,
        timeframe: '5m'
      },
      'breakout': {
        breakoutLookback: 20,
        confirmRetest: false,
        timeframe: '5m'
      },
      'test': {
        testAmount: 0.10,
        retryIntervalMinutes: 3,
        maxRetries: 10,
        timeframe: '5m'
      },
      'trailing-entry': {
        trailingLookback: 10,
        recoveryPercent: 0.03,
        timeframe: '5m'
      },
      'simple': {
        stopLossPercent: 0.025,
        timeframe: '5m'
      }
    };

    return defaults[strategyName] || {};
  }

  // Validate and fix a single strategy configuration
  validateStrategyConfig(strategyConfig) {
    const { name, allocation, config } = strategyConfig;
    const fixes = [];
    
    // Validate strategy name
    if (!name || typeof name !== 'string') {
      this.validationErrors.push('Strategy missing valid name');
      return null;
    }

    // Validate allocation
    let validAllocation = allocation;
    if (typeof allocation !== 'number' || allocation <= 0 || allocation > 1) {
      validAllocation = 0.1; // Default 10%
      fixes.push(`Fixed invalid allocation: ${allocation} → ${validAllocation}`);
    }

    // Get required defaults for this strategy
    const requiredDefaults = this.getStrategyDefaults(name);
    
    // Merge config with defaults, preserving existing values
    const validConfig = { ...requiredDefaults, ...config };
    
    // Check for missing required parameters
    Object.entries(requiredDefaults).forEach(([key, defaultValue]) => {
      if (config[key] === undefined) {
        fixes.push(`Added missing parameter: ${key} = ${defaultValue}`);
      }
    });

    // Validate specific parameter ranges
    const parameterFixes = this.validateParameterRanges(name, validConfig);
    fixes.push(...parameterFixes);

    if (fixes.length > 0) {
      this.fixesApplied.push({
        strategy: name,
        fixes: fixes
      });
    }

    return {
      name,
      allocation: validAllocation,
      config: validConfig
    };
  }

  // Validate parameter ranges for specific strategies
  validateParameterRanges(strategyName, config) {
    const fixes = [];

    // Common validations
    if (config.accountPercentToUse !== undefined) {
      if (config.accountPercentToUse <= 0 || config.accountPercentToUse > 1) {
        config.accountPercentToUse = 0.5;
        fixes.push('Fixed accountPercentToUse to valid range (0-1)');
      }
    }

    // Validate timeframe
    const validTimeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d'];
    if (config.timeframe !== undefined) {
      if (!validTimeframes.includes(config.timeframe)) {
        config.timeframe = '5m';
        fixes.push(`Fixed invalid timeframe to default '5m' (valid: ${validTimeframes.join(', ')})`);
      }
    }

    // Strategy-specific validations
    switch (strategyName) {
      case 'trend-following':
        if (config.minTrendLookback < 5 || config.minTrendLookback > 100) {
          config.minTrendLookback = 20;
          fixes.push('Fixed minTrendLookback to valid range (5-100)');
        }
        if (config.recentHighThreshold < 0.9 || config.recentHighThreshold > 1) {
          config.recentHighThreshold = 0.98;
          fixes.push('Fixed recentHighThreshold to valid range (0.9-1.0)');
        }
        if (config.shortTermPeriod < 3 || config.shortTermPeriod > 20) {
          config.shortTermPeriod = 5;
          fixes.push('Fixed shortTermPeriod to valid range (3-20)');
        }
        break;

      case 'buy-the-dip':
        if (config.dipThreshold < 0.01 || config.dipThreshold > 0.2) {
          config.dipThreshold = 0.05;
          fixes.push('Fixed dipThreshold to valid range (0.01-0.2)');
        }
        if (config.dipLookback < 2 || config.dipLookback > 20) {
          config.dipLookback = 5;
          fixes.push('Fixed dipLookback to valid range (2-20)');
        }
        if (config.minTrendLookback < 5 || config.minTrendLookback > 100) {
          config.minTrendLookback = 20;
          fixes.push('Fixed minTrendLookback to valid range (5-100)');
        }
        break;

      case 'mean-reversion':
        if (config.deviationThreshold < 0.01 || config.deviationThreshold > 0.1) {
          config.deviationThreshold = 0.04;
          fixes.push('Fixed deviationThreshold to valid range (0.01-0.1)');
        }
        if (config.meanPeriod < 5 || config.meanPeriod > 50) {
          config.meanPeriod = 20;
          fixes.push('Fixed meanPeriod to valid range (5-50)');
        }
        break;

      case 'breakout':
        if (config.breakoutLookback < 5 || config.breakoutLookback > 50) {
          config.breakoutLookback = 20;
          fixes.push('Fixed breakoutLookback to valid range (5-50)');
        }
        break;

      case 'moving-average-crossover':
        if (config.shortPeriod < 3 || config.shortPeriod > 20) {
          config.shortPeriod = 5;
          fixes.push('Fixed shortPeriod to valid range (3-20)');
        }
        if (config.longPeriod < 10 || config.longPeriod > 50) {
          config.longPeriod = 20;
          fixes.push('Fixed longPeriod to valid range (10-50)');
        }
        if (config.shortPeriod >= config.longPeriod) {
          config.shortPeriod = 5;
          config.longPeriod = 20;
          fixes.push('Fixed period conflict: shortPeriod must be less than longPeriod');
        }
        break;

      case 'range-trading':
        if (config.rangeLookback < 10 || config.rangeLookback > 50) {
          config.rangeLookback = 20;
          fixes.push('Fixed rangeLookback to valid range (10-50)');
        }
        if (config.entryBufferPercent < 0.01 || config.entryBufferPercent > 0.1) {
          config.entryBufferPercent = 0.02;
          fixes.push('Fixed entryBufferPercent to valid range (0.01-0.1)');
        }
        break;

      case 'trailing-entry':
        if (config.trailingLookback < 5 || config.trailingLookback > 30) {
          config.trailingLookback = 10;
          fixes.push('Fixed trailingLookback to valid range (5-30)');
        }
        if (config.recoveryPercent < 0.01 || config.recoveryPercent > 0.1) {
          config.recoveryPercent = 0.03;
          fixes.push('Fixed recoveryPercent to valid range (0.01-0.1)');
        }
        break;

      case 'volatility-weighted':
        if (config.riskFactor < 0.005 || config.riskFactor > 0.05) {
          config.riskFactor = 0.02;
          fixes.push('Fixed riskFactor to valid range (0.005-0.05)');
        }
        if (config.minTrendLookback < 5 || config.minTrendLookback > 100) {
          config.minTrendLookback = 20;
          fixes.push('Fixed minTrendLookback to valid range (5-100)');
        }
        break;
    }

    return fixes;
  }

  // Validate entire portfolio configuration
  validatePortfolioConfig(config) {
    if (!config.portfolio) {
      config.portfolio = {
        strategies: [],
        rebalanceFrequency: 86400000,
        performanceWindow: 604800000,
        minAllocation: 0.025, // Reduced from 0.05 to support smaller portfolios
        maxAllocation: 0.6,
        rebalanceThreshold: 0.1
      };
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: ['Created missing portfolio configuration']
      });
    }

    const portfolio = config.portfolio;
    
    // Validate portfolio-level settings
    if (!portfolio.strategies || !Array.isArray(portfolio.strategies)) {
      portfolio.strategies = [];
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: ['Fixed missing or invalid strategies array']
      });
    }

    // Validate and fix each strategy
    const validStrategies = [];
    let totalAllocation = 0;
    
    portfolio.strategies.forEach(strategyConfig => {
      const validStrategy = this.validateStrategyConfig(strategyConfig);
      if (validStrategy) {
        validStrategies.push(validStrategy);
        totalAllocation += validStrategy.allocation;
      }
    });

    // Normalize allocations to sum to 1.0
    if (validStrategies.length > 0 && Math.abs(totalAllocation - 1.0) > 0.001) {
      validStrategies.forEach(strategy => {
        strategy.allocation = strategy.allocation / totalAllocation;
      });
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: [`Normalized allocations to sum to 1.0 (was ${totalAllocation.toFixed(3)})`]
      });
    }

    portfolio.strategies = validStrategies;
    
    // Validate portfolio settings
    const portfolioDefaults = {
      rebalanceFrequency: 86400000,
      performanceWindow: 604800000,
      minAllocation: 0.025, // Reduced from 0.05 to support smaller portfolios
      maxAllocation: 0.6,
      rebalanceThreshold: 0.1
    };
    
    Object.entries(portfolioDefaults).forEach(([key, defaultValue]) => {
      if (portfolio[key] === undefined) {
        portfolio[key] = defaultValue;
        this.fixesApplied.push({
          strategy: 'portfolio',
          fixes: [`Added missing portfolio setting: ${key} = ${defaultValue}`]
        });
      }
    });

    // Validate rebalance frequency range (1 hour to 1 week)
    const minRebalanceFreq = 60 * 60 * 1000; // 1 hour in ms
    const maxRebalanceFreq = 7 * 24 * 60 * 60 * 1000; // 1 week in ms

    if (portfolio.rebalanceFrequency < minRebalanceFreq) {
      portfolio.rebalanceFrequency = minRebalanceFreq;
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: [`Rebalance frequency too low, set to minimum 1 hour`]
      });
    } else if (portfolio.rebalanceFrequency > maxRebalanceFreq) {
      portfolio.rebalanceFrequency = maxRebalanceFreq;
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: [`Rebalance frequency too high, set to maximum 1 week`]
      });
    }

    return config;
  }

  // Validate rebalance frequency value
  validateRebalanceFrequency(frequency) {
    const minRebalanceFreq = 60 * 60 * 1000; // 1 hour in ms
    const maxRebalanceFreq = 7 * 24 * 60 * 60 * 1000; // 1 week in ms

    if (typeof frequency !== 'number' || frequency < minRebalanceFreq) {
      return {
        isValid: false,
        corrected: minRebalanceFreq,
        error: 'Rebalance frequency must be at least 1 hour'
      };
    }

    if (frequency > maxRebalanceFreq) {
      return {
        isValid: false,
        corrected: maxRebalanceFreq,
        error: 'Rebalance frequency cannot exceed 1 week'
      };
    }

    return { isValid: true, corrected: frequency };
  }

  // Main validation function
  validateConfig(config) {
    this.validationErrors = [];
    this.fixesApplied = [];
    
    // Ensure basic structure
    if (!config || typeof config !== 'object') {
      config = {};
    }

    // Validate max trading balance
    if (config.maxTradingBalance !== undefined &&
        (typeof config.maxTradingBalance !== 'number' || config.maxTradingBalance < 0)) {
      config.maxTradingBalance = 0; // Default to 0 instead of null
      this.fixesApplied.push({
        strategy: 'global',
        fixes: ['Reset invalid maxTradingBalance to 0']
      });
    }

    // Validate portfolio configuration
    config = this.validatePortfolioConfig(config);
    
    // Add timestamp
    config.lastUpdated = new Date().toISOString();
    
    return {
      config,
      isValid: this.validationErrors.length === 0,
      errors: this.validationErrors,
      fixes: this.fixesApplied
    };
  }

  // Generate validation report
  generateReport(validationResult) {
    const { config, isValid, errors, fixes } = validationResult;

    let report = '\n=== Configuration Validation Report ===\n';

    if (isValid) {
      report += '✅ Configuration is valid\n';
    } else {
      report += '❌ Configuration has errors:\n';
      errors.forEach(error => {
        report += `  - ${error}\n`;
      });
    }

    if (fixes.length > 0) {
      report += '\n🔧 Fixes applied:\n';
      fixes.forEach(fix => {
        report += `  ${fix.strategy}:\n`;
        fix.fixes.forEach(f => {
          report += `    - ${f}\n`;
        });
      });

      // Check if timeframe fixes were applied
      const timeframeFixes = fixes.some(fix =>
        fix.fixes.some(f => f.includes('timeframe'))
      );

      if (timeframeFixes) {
        report += '\n📊 Timeframe Update:\n';
        report += '  Strategies now use time-based candles instead of raw price updates.\n';
        report += '  This provides more accurate technical analysis aligned with trading standards.\n';
        report += '  Default: 5-minute candles (good for day trading)\n';
        report += '  Configure via strategy settings: 1m, 5m, 15m, 30m, 1h, 4h, 1d\n';
      }
    }

    report += `\nStrategies configured: ${config.portfolio?.strategies?.length || 0}\n`;
    report += `Max trading balance: ${config.maxTradingBalance ? '$' + config.maxTradingBalance : 'Unlimited'}\n`;

    // Show timeframe summary
    if (config.portfolio?.strategies) {
      const timeframes = {};
      config.portfolio.strategies.forEach(strategy => {
        const tf = strategy.config.timeframe || '5m';
        timeframes[tf] = (timeframes[tf] || 0) + 1;
      });

      if (Object.keys(timeframes).length > 0) {
        report += 'Timeframes in use: ';
        report += Object.entries(timeframes)
          .map(([tf, count]) => `${count}×${tf}`)
          .join(', ') + '\n';
      }
    }

    report += '=====================================\n';

    return report;
  }
}

// Convenience function to validate and fix config file
export async function validateConfigFile(configPath = 'data/bot-data.json') {
  const validator = new ConfigValidator();
  
  try {
    // Read existing config
    let config = {};
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      config = JSON.parse(configData);
    }
    
    // Validate and fix
    const result = validator.validateConfig(config);
    
    // Write back fixed config
    fs.writeFileSync(configPath, JSON.stringify(result.config, null, 2));
    
    // Generate and log report
    const report = validator.generateReport(result);
    console.log(report);
    
    return result;
  } catch (error) {
    console.error('Error validating config:', error.message);
    return {
      config: {},
      isValid: false,
      errors: [error.message],
      fixes: []
    };
  }
}
