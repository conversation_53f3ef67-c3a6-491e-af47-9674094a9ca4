// Global variables
console.log('🚀 Script.js loading...');
console.log('📋 Defining global variables...');

// DOM elements - will be initialized after DOM loads
let balanceEl, cryptoHoldingsEl, portfolioValueEl, pnlEl, logEl;
let strategySelect, applyStrategyBtn, maxBalanceSlider, maxBalanceValue;
let availableBalanceEl, activeStrategiesEl, lastRebalanceEl, rebalanceFrequencyEl, nextRebalanceEl, rebalanceBtn;
let symbolSelect, holdingsTitleEl, priceTitleEl, chartTitleEl;
let priceIcon, priceLabel;
let stickyMarketTicker;
let holdingsElements = {}; // Will store references to holdings cards

// Symbol allocation management
let symbolAllocations = {};
let activeSymbols = [];
let allocationSliders = {};

console.log('✅ Global variables defined successfully');

let availableStrategies = {};
let currentStrategyConfig = {};
let currentAvailableBalance = 0;
let maxTradingBalance = 0; // Will be loaded from server
let rebalanceFrequency = 24; // hours
let botRunning = false;
let rebalanceCountdownTimer = null;
let lastRebalanceData = null;
let priceChart;
let portfolioChart;
let priceHistory = [];
let portfolioHistory = [];
let tradeEvents = [];
let currentTimeframe = '1m';
let currentPeriod = '1d';
let currentSymbol = 'BTC-USD'; // Current trading symbol - will be updated to first active symbol
let availableSymbols = []; // Will be loaded from server

// Symbol configuration
const symbolConfig = {
  'BTC-USD': { icon: '₿', name: 'Bitcoin', code: 'BTC' },
  'ETH-USD': { icon: 'Ξ', name: 'Ethereum', code: 'ETH' },
  'DOGE-USD': { icon: 'Ð', name: 'Dogecoin', code: 'DOGE' },
  'LTC-USD': { icon: 'Ł', name: 'Litecoin', code: 'LTC' },
  'BCH-USD': { icon: '₿', name: 'Bitcoin Cash', code: 'BCH' },
  'XRP-USD': { icon: '◉', name: 'XRP', code: 'XRP' }
};

// Price tracking for market summary
let symbolPrices = {
  'BTC-USD': { price: 0, change: 0 },
  'ETH-USD': { price: 0, change: 0 },
  'DOGE-USD': { price: 0, change: 0 },
  'LTC-USD': { price: 0, change: 0 },
  'BCH-USD': { price: 0, change: 0 },
  'XRP-USD': { price: 0, change: 0 }
};

console.log('📊 Chart variables initialized');

// Dynamic symbol configuration loading
async function loadActiveSymbols() {
  try {
    const response = await fetch('/api/active-symbols');
    const data = await response.json();

    if (data.success) {
      activeSymbols = data.activeSymbols;
      console.log(`📊 Loaded ${activeSymbols.length} active symbols from ${data.source}:`, activeSymbols);

      // Set currentSymbol to first active symbol if not already set to an active symbol
      if (!activeSymbols.includes(currentSymbol)) {
        currentSymbol = activeSymbols[0] || 'BTC-USD';
        console.log(`📊 Updated currentSymbol to: ${currentSymbol}`);
      }

      // Initialize symbolPrices for all active symbols
      symbolPrices = {};
      activeSymbols.forEach(symbol => {
        symbolPrices[symbol] = { price: 0, change: 0 };
      });

      // Update the sticky ticker with active symbols
      updateStickyTicker();

      return activeSymbols;
    } else {
      console.error('Failed to load active symbols:', data.error);
      return [];
    }
  } catch (error) {
    console.error('Error loading active symbols:', error);
    return [];
  }
}

// Update sticky ticker with current active symbols
function updateStickyTicker() {
  const tickerContainer = document.getElementById('stickyMarketTicker');
  if (tickerContainer && activeSymbols.length > 0) {
    tickerContainer.innerHTML = activeSymbols.map(symbol => {
      const config = symbolConfig[symbol] || { code: symbol.split('-')[0] };
      return `<span class="ticker-item">${config.code}: $0.00 (+0.00%)</span>`;
    }).join('');
  }
}

// Symbol management functions
function handleSymbolChange(event) {
  const newSymbol = event ? event.target.value : symbolSelect.value;
  console.log(`🔄 Symbol changed from ${currentSymbol} to ${newSymbol}`);

  currentSymbol = newSymbol;
  updateUILabels();
  updateEnhancedSymbolSelector();

  // Clear existing chart data
  priceHistory = [];
  portfolioHistory = [];

  // Clear chart display immediately to show loading state
  if (priceChart) {
    priceChart.data.datasets[0].data = [];
    priceChart.update('none'); // Quick update to clear chart
    console.log(`Cleared chart data for symbol change to ${newSymbol}`);
  }

  // Refresh data for new symbol
  console.log(`Fetching new data for ${newSymbol}...`);
  updateCharts();
  updateStatus();
}

function updateUILabels() {
  const assetCode = currentSymbol.split('-')[0];

  if (holdingsTitleEl) {
    holdingsTitleEl.textContent = `${assetCode} Holdings`;
  }

  if (priceTitleEl) {
    priceTitleEl.textContent = `Current ${assetCode} Price`;
  }

  if (chartTitleEl) {
    chartTitleEl.textContent = `${assetCode} Price Chart (OHLC)`;
  }

  console.log(`✅ UI labels updated for ${currentSymbol}`);
}

// Update enhanced symbol selector with current symbol info
function updateEnhancedSymbolSelector() {
  const config = symbolConfig[currentSymbol];
  if (!config) return;

  // Update price card
  if (priceIcon && priceLabel) {
    priceIcon.textContent = config.icon;
    priceLabel.textContent = `Current ${config.code} Price`;
  }

  // Update symbol button states
  updateSymbolButtonStates(currentSymbol);

  // Update holdings card visibility
  updateHoldingsCardVisibility();

  console.log(`✅ Enhanced symbol selector updated for ${currentSymbol}`);
}

// Update symbol button active states
function updateSymbolButtonStates(activeSymbol) {
  const symbolButtons = document.querySelectorAll('.symbol-btn');
  symbolButtons.forEach(btn => {
    const symbol = btn.getAttribute('data-symbol');
    if (symbol === activeSymbol) {
      btn.classList.add('active');
    } else {
      btn.classList.remove('active');
    }
  });
}

// Update market summary ticker (both sticky and any other tickers)
function updateMarketSummary() {
  const tickerItems = [];
  Object.keys(symbolPrices).forEach(symbol => {
    const config = symbolConfig[symbol];
    const priceData = symbolPrices[symbol];
    if (config && priceData) {
      const changeClass = priceData.change >= 0 ? 'positive' : 'negative';
      const changeSign = priceData.change >= 0 ? '+' : '';
      tickerItems.push(
        `<span class="ticker-item ${changeClass}">${config.code}: $${priceData.price.toFixed(2)} (${changeSign}${priceData.change.toFixed(2)}%)</span>`
      );
    }
  });

  const tickerHTML = tickerItems.join('');

  // Update sticky ticker
  if (stickyMarketTicker) {
    stickyMarketTicker.innerHTML = tickerHTML;
  }

  console.log('✅ Market summary updated');
}

// Update symbol button prices
function updateSymbolButtonPrices() {
  Object.keys(symbolPrices).forEach(symbol => {
    const config = symbolConfig[symbol];
    const priceData = symbolPrices[symbol];
    if (config && priceData) {
      const priceElement = document.getElementById(`${config.code.toLowerCase()}-price`);
      if (priceElement) {
        // Format price based on value for better readability
        let formattedPrice;
        if (priceData.price >= 1000) {
          formattedPrice = `$${priceData.price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        } else if (priceData.price >= 1) {
          formattedPrice = `$${priceData.price.toFixed(2)}`;
        } else {
          formattedPrice = `$${priceData.price.toFixed(4)}`;
        }
        priceElement.textContent = formattedPrice;
      }
    }
  });
}

// Update current symbol price and change
function updateCurrentSymbolPrice(symbol, price, change) {
  // Update price tracking
  symbolPrices[symbol] = { price, change };

  // Update market summary and button prices
  updateMarketSummary();
  updateSymbolButtonPrices();
}

// Update holdings card visibility - show all currency holdings for complete portfolio view
function updateHoldingsCardVisibility() {
  // Show holdings cards for ALL currencies - this is a multi-currency trading dashboard
  Object.keys(symbolConfig).forEach(symbol => {
    const config = symbolConfig[symbol];
    const cardId = `${config.code.toLowerCase()}-holdings-card`;
    const card = document.getElementById(cardId);
    if (card) {
      card.style.display = 'block';
    }
  });

  console.log(`📊 Showing holdings cards for all currencies: ${Object.keys(symbolConfig).join(', ')}`);
}

// Update holdings for a specific symbol
function updateSymbolHoldings(symbol, holdings, value) {
  const config = symbolConfig[symbol];
  if (!config) return;

  const holdingsId = `${config.code.toLowerCase()}-holdings`;
  const valueId = `${config.code.toLowerCase()}-value`;

  const holdingsElement = document.getElementById(holdingsId);
  const valueElement = document.getElementById(valueId);

  if (holdingsElement) {
    holdingsElement.textContent = holdings.toFixed(8);
  }

  if (valueElement) {
    valueElement.textContent = `$${value.toFixed(2)}`;
  }

  console.log(`💰 Updated ${symbol} holdings: ${holdings.toFixed(8)} ($${value.toFixed(2)})`);

  // Update card visibility after holdings change
  updateHoldingsCardVisibility();
}

function getSymbolDisplayName(symbol) {
  const symbolMap = {
    'BTC-USD': 'Bitcoin',
    'ETH-USD': 'Ethereum',
    'DOGE-USD': 'Dogecoin',
    'LTC-USD': 'Litecoin',
    'BCH-USD': 'Bitcoin Cash',
    'XRP-USD': 'XRP'
  };
  return symbolMap[symbol] || symbol;
}

// Load available strategies from server
console.log('🔧 Defining functions...');
async function loadStrategies() {
  try {
    console.log('Loading strategies...');
    const response = await fetch('/api/strategies');
    console.log('Response status:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      console.error('Expected JSON but got:', text.substring(0, 200));
      throw new Error('Server returned HTML instead of JSON - check server logs');
    }

    const data = await response.json();
    console.log('Strategies data:', data);

    availableStrategies = {};

    // Always populate availableStrategies, regardless of UI elements
    if (data.strategies && Array.isArray(data.strategies)) {
      data.strategies.forEach(strategy => {
        console.log('Adding strategy:', strategy);
        availableStrategies[strategy.id] = strategy;
      });
      console.log(`Loaded ${data.strategies.length} strategies into availableStrategies`);
    }

    // Update UI elements if they exist
    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Choose a strategy...</option>';

      if (data.strategies && Array.isArray(data.strategies)) {
        data.strategies.forEach(strategy => {
          const option = document.createElement('option');
          option.value = strategy.id;
          option.textContent = strategy.displayName;
          strategySelect.appendChild(option);
        });
      }

      const configGridEl = document.getElementById('configGrid');
      if (configGridEl) {
        updateStrategyUI();
      } else {
        console.log('Strategy UI elements not found, skipping UI update');
      }
    } else {
      console.log('Strategy select element not found, but strategies still loaded');
    }
  } catch (error) {
    console.error('Failed to load strategies:', error);
    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Error loading strategies</option>';
    }
  }
}

function updateStrategyUI() {
  const configGridEl = document.getElementById('configGrid');

  if (!strategySelect || !configGridEl) {
    console.warn('Required strategy UI elements not found');
    return;
  }

  const selectedStrategyId = strategySelect.value;
  const strategy = availableStrategies[selectedStrategyId];

  if (!strategy) return;

  configGridEl.innerHTML = '';
  currentStrategyConfig = {};

  if (strategy.config && Object.keys(strategy.config).length > 0) {
    Object.entries(strategy.config).forEach(([key, configItem]) => {
      const configRow = document.createElement('div');
      configRow.className = 'config-item';

      const label = document.createElement('label');
      label.textContent = configItem.label || key;
      label.setAttribute('for', key);

      const input = document.createElement('input');
      input.type = configItem.type || 'number';
      input.id = key;
      input.value = configItem.default || '';

      if (configItem.min !== undefined) input.min = configItem.min;
      if (configItem.max !== undefined) input.max = configItem.max;
      if (configItem.step !== undefined) input.step = configItem.step;
      if (configItem.description) input.title = configItem.description;

      input.addEventListener('input', (e) => {
        currentStrategyConfig[key] = configItem.type === 'number' ?
          parseFloat(e.target.value) : e.target.value;
      });

      currentStrategyConfig[key] = configItem.default || '';

      configRow.appendChild(label);
      configRow.appendChild(input);
      configGridEl.appendChild(configRow);
    });
  }
}

// Load saved settings on page load
async function loadSavedSettings() {
  try {
    // Load max balance
    const balanceResponse = await fetch('/api/max-balance');
    if (balanceResponse.ok) {
      const contentType = balanceResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const balanceData = await balanceResponse.json();
        if (balanceData.maxBalance !== null && balanceData.maxBalance !== undefined) {
          maxTradingBalance = balanceData.maxBalance;

          if (maxBalanceSlider && maxBalanceValue) {
            maxBalanceSlider.value = maxTradingBalance;
            maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
          }
        }
      }
    }

    // Load current strategy
    const strategyResponse = await fetch('/api/strategy');
    if (strategyResponse.ok) {
      const contentType = strategyResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const strategyData = await strategyResponse.json();
        if (strategyData.name) {
          const strategyKey = strategyData.name.toLowerCase().replace(/strategy$/, '').replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
          if (availableStrategies[strategyKey]) {
            if (strategySelect) {
              strategySelect.value = strategyKey;
              updateStrategyUI();
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to load saved settings:', error);
  }
}

// Calculate and format next rebalance countdown
function updateNextRebalanceCountdown(lastRebalance, rebalanceFrequencyMs) {
  if (!nextRebalanceEl || !lastRebalance || !rebalanceFrequencyMs) return;

  // Store data for timer updates
  lastRebalanceData = { lastRebalance, rebalanceFrequencyMs };

  const lastRebalanceTime = new Date(lastRebalance).getTime();
  const nextRebalanceTime = lastRebalanceTime + rebalanceFrequencyMs;
  const now = Date.now();
  const timeUntilRebalance = nextRebalanceTime - now;

  if (timeUntilRebalance <= 0) {
    nextRebalanceEl.textContent = 'Due now';
    nextRebalanceEl.className = 'stat-value negative';
    return;
  }

  // Format countdown
  const hours = Math.floor(timeUntilRebalance / (1000 * 60 * 60));
  const minutes = Math.floor((timeUntilRebalance % (1000 * 60 * 60)) / (1000 * 60));

  let countdownText;
  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    countdownText = `${days}d ${remainingHours}h`;
  } else if (hours > 0) {
    countdownText = `${hours}h ${minutes}m`;
  } else {
    countdownText = `${minutes}m`;
  }

  nextRebalanceEl.textContent = countdownText;
  nextRebalanceEl.className = 'stat-value';

  // Start countdown timer if not already running
  if (!rebalanceCountdownTimer) {
    rebalanceCountdownTimer = setInterval(() => {
      if (lastRebalanceData) {
        updateNextRebalanceCountdown(lastRebalanceData.lastRebalance, lastRebalanceData.rebalanceFrequencyMs);
      }
    }, 60000); // Update every minute
  }
}

// Update max trading balance
async function updateMaxTradingBalance(balance) {
  try {
    const response = await fetch('/api/max-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ maxBalance: balance })
    });

    if (!response.ok) {
      console.error('Failed to update max trading balance');
    }
  } catch (error) {
    console.error('Error updating max trading balance:', error);
  }
}



// Update rebalance frequency
async function updateRebalanceFrequency(hours) {
  try {
    const response = await fetch('/api/portfolio/rebalance-frequency', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ frequency: hours * 60 * 60 * 1000 }) // Convert hours to milliseconds
    });

    if (!response.ok) {
      console.error('Failed to update rebalance frequency');
    }
  } catch (error) {
    console.error('Error updating rebalance frequency:', error);
  }
}

console.log('✅ First batch of functions defined');

// Update slider when balance changes
function updateBalanceSlider(availableBalance) {
  const newBalance = parseFloat(availableBalance) || 0;
  console.log(`💰 updateBalanceSlider: old=${currentAvailableBalance}, new=${newBalance}`);

  currentAvailableBalance = newBalance;
  if (availableBalanceEl) {
    availableBalanceEl.textContent = currentAvailableBalance.toFixed(2);
  }

  if (maxBalanceSlider) {
    const oldMax = maxBalanceSlider.max;
    const newMax = currentAvailableBalance; // Use actual balance, no minimum
    maxBalanceSlider.max = newMax;

    console.log(`🎚️ Slider max updated: ${oldMax} → ${newMax}`);

    if (maxTradingBalance > currentAvailableBalance) {
      console.log(`⚠️ Max trading balance ${maxTradingBalance} > available ${currentAvailableBalance}, adjusting...`);
      maxTradingBalance = currentAvailableBalance;
      maxBalanceSlider.value = maxTradingBalance;
      if (maxBalanceValue) {
        maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
      }
      updateMaxTradingBalance(maxTradingBalance);
    }
  }
}

// Initialize both charts
function initializeChart() {
  initializePortfolioChart();
  initializePriceChart();
  initializeChartControls();
  console.log('Charts initialized successfully');
}

// Initialize portfolio balance chart
function initializePortfolioChart() {
  const ctx = document.getElementById('portfolioChart').getContext('2d');

  portfolioChart = new Chart(ctx, {
    type: 'line',
    data: {
      datasets: [
        {
          label: 'Total Value',
          data: [],
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        },
        {
          label: 'Cash Balance',
          data: [],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.05)',
          borderWidth: 1,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        },
        {
          label: 'Holdings Value',
          data: [],
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.05)',
          borderWidth: 1,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        title: {
          display: true,
          text: 'Portfolio Balance (Last 24 Hours)',
          color: '#9ca3af',
          font: {
            size: 14,
            weight: 'normal'
          },
          padding: {
            bottom: 10
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: '#9ca3af',
            usePointStyle: true,
            pointStyle: 'line'
          }
        },
        tooltip: {
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          titleColor: '#ffffff',
          bodyColor: '#9ca3af',
          borderColor: '#374151',
          borderWidth: 1,
          cornerRadius: 8,
          callbacks: {
            title: function (context) {
              return new Date(context[0].parsed.x).toLocaleString();
            },
            label: function (context) {
              return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'hour',
            displayFormats: {
              hour: 'HH:mm',
              day: 'MMM dd',
              month: 'MMM yyyy'
            }
          },
          grid: {
            color: '#374151',
            drawBorder: false
          },
          ticks: {
            color: '#9ca3af',
            maxTicksLimit: 6
          }
        },
        y: {
          grid: {
            color: '#374151',
            drawBorder: false
          },
          ticks: {
            color: '#9ca3af',
            callback: function (value) {
              return '$' + value.toFixed(0);
            }
          }
        }
      },
      animation: {
        duration: 0
      }
    }
  });
}

// Initialize OHLC candlestick chart
function initializePriceChart() {
  console.log('🔧 Initializing price chart...');
  const ctx = document.getElementById('priceChart').getContext('2d');

  Chart.defaults.font.family = "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif";
  Chart.defaults.font.size = 12;
  Chart.defaults.color = '#9ca3af';

  // Test if candlestick controller is available
  console.log('Available chart types:', Object.keys(Chart.registry.controllers.items));
  console.log('Candlestick controller available:', !!Chart.registry.getController('candlestick'));

  // Try candlestick chart first, fallback to line chart if plugin not available
  const chartType = Chart.registry.getController('candlestick') ? 'candlestick' : 'line';
  console.log(`Using chart type: ${chartType}`);

  if (chartType === 'candlestick') {
    priceChart = new Chart(ctx, {
      type: 'candlestick',
      data: {
        datasets: [{
          label: currentSymbol,
          data: [],
          borderColor: '#10b981',
          backgroundColor: '#ef4444',
          borderWidth: 1,
          color: {
            up: 'rgb(195, 245, 60)',
            down: '#ef4444',
            unchanged: '#9ca3af'
          }
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#9ca3af',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              title: function (context) {
                const isForming = context[0].datasetIndex === 1; // Second dataset is forming candles
                const timeStr = new Date(context[0].parsed.x).toLocaleString();
                return isForming ? `${timeStr} (Forming)` : timeStr;
              },
              label: function (context) {
                const data = context.parsed;
                const isForming = context.datasetIndex === 1;

                // Handle undefined values safely
                const open = data.o != null ? data.o.toFixed(2) : 'N/A';
                const high = data.h != null ? data.h.toFixed(2) : 'N/A';
                const low = data.l != null ? data.l.toFixed(2) : 'N/A';
                const close = data.c != null ? data.c.toFixed(2) : 'N/A';

                const labels = [
                  `Open: $${open}`,
                  `High: $${high}`,
                  `Low: $${low}`,
                  `Close: $${close}`
                ];

                if (isForming) {
                  labels.push('⏱️ Candle is still forming');
                }

                return labels;
              }
            }
          },
          annotation: {
            annotations: {}
          }
        },
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            grid: {
              color: '#374151',
              drawBorder: false
            },
            ticks: {
              color: '#9ca3af',
              maxTicksLimit: 8,
              callback: function (value) {
                return new Date(value).toLocaleTimeString();
              }
            }
          },
          y: {
            type: 'linear',
            beginAtZero: false,
            grid: {
              color: '#374151',
              drawBorder: false
            },
            ticks: {
              color: '#9ca3af',
              callback: function (value) {
                return '$' + value.toLocaleString();
              }
            }
          }
        },
        animation: {
          duration: 0
        }
      }
    });
  }
}

// Initialize chart controls
function initializeChartControls() {
  console.log('🎮 Initializing chart controls...');

  // Timeframe buttons (for candle timeframe)
  const timeframeButtons = document.querySelectorAll('.timeframe-btn');
  console.log(`Found ${timeframeButtons.length} timeframe buttons`);

  timeframeButtons.forEach(button => {
    button.addEventListener('click', () => {
      console.log(`🔄 Timeframe changed to: ${button.dataset.timeframe}`);
      document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
      button.classList.add('active');
      currentTimeframe = button.dataset.timeframe;
      console.log('📊 Updating charts immediately...');
      updateCharts();
    });
  });

  // Period buttons (for time range)
  document.querySelectorAll('.period-btn').forEach(button => {
    button.addEventListener('click', () => {
      document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
      button.classList.add('active');
      currentPeriod = button.dataset.period;
      updateCharts();
    });
  });
}

// Update both charts with new data
async function updateCharts() {
  await updatePriceChart();
  await updatePortfolioChart();
}

// Update chart label and title for current symbol
function updateChartLabelsForSymbol() {
  if (priceChart && priceChart.data.datasets[0]) {
    // Update dataset label
    priceChart.data.datasets[0].label = currentSymbol;
    console.log(`Updated chart label to: ${currentSymbol}`);
  }
}

// Update OHLC candlestick chart
async function updatePriceChart() {
  if (!priceChart) {
    console.warn('Price chart not initialized, skipping update');
    return;
  }

  // Update chart labels for current symbol
  updateChartLabelsForSymbol();

  try {
    console.log(`Fetching candle data for ${currentSymbol}...`);
    let response = await fetch(`/api/candles/${currentTimeframe}?period=${currentPeriod}&symbol=${currentSymbol}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    let data = await response.json();

    // If no candles for current timeframe, try 1m as fallback
    if (data.count === 0 && currentTimeframe !== '1m') {
      console.log(`No ${currentTimeframe} candles for ${currentSymbol}, trying 1m as fallback`);
      response = await fetch(`/api/candles/1m?period=${currentPeriod}&symbol=${currentSymbol}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      data = await response.json();
      console.log(`Fallback to 1m: got ${data.count} candles for ${currentSymbol}`);
    } else {
      console.log(`Using ${currentTimeframe}: got ${data.count} candles for ${currentSymbol}`);
    }

    console.log(`Chart update: ${data.count} candles for ${data.timeframe}`, data);

    if (data.candles && data.candles.length > 0) {
      // Validate candle data format
      const validCandles = data.candles.filter(candle =>
        candle.t && typeof candle.o === 'number' && typeof candle.h === 'number' &&
        typeof candle.l === 'number' && typeof candle.c === 'number'
      );

      if (validCandles.length === 0) {
        console.warn('No valid candle data found');
        return;
      }

      // Convert to Chart.js candlestick format
      const formatCandleForChart = (candle) => ({
        x: candle.t,
        o: candle.o,
        h: candle.h,
        l: candle.l,
        c: candle.c
      });

      // Use single dataset with all candles
      const allCandles = validCandles.map(formatCandleForChart);

      console.log(`Total candles: ${allCandles.length} (${allCandles.filter(c => !c.isForming).length} completed, ${allCandles.filter(c => c.isForming).length} forming)`);

      // Log sample data for debugging
      if (allCandles.length > 0) {
        const sample = allCandles[0];
        console.log(`Sample formatted candle:`, sample);
        console.log(`OHLC values: O:${sample.o}, H:${sample.h}, L:${sample.l}, C:${sample.c}`);
      }

      // Update single dataset with all candles
      priceChart.data.datasets[0].data = allCandles;
      console.log(`Set all candles:`, allCandles.length);



      // Calculate Y-axis bounds from the data
      if (allCandles.length > 0) {
        const allPrices = allCandles.flatMap(candle => [candle.h, candle.l]);
        const minPrice = Math.min(...allPrices);
        const maxPrice = Math.max(...allPrices);
        const padding = (maxPrice - minPrice) * 0.1; // 10% padding

        priceChart.options.scales.y.min = Math.max(0, minPrice - padding);
        priceChart.options.scales.y.max = maxPrice + padding;

        console.log(`Y-axis bounds: ${priceChart.options.scales.y.min.toFixed(0)} - ${priceChart.options.scales.y.max.toFixed(0)}`);
      }

      // Debug: Check what Chart.js actually has
      console.log('Chart dataset after update:', priceChart.data.datasets[0].data.length, 'candles');

      // Update chart with error handling
      try {
        priceChart.update('active'); // Use 'active' instead of 'none' for better rendering
        console.log('Chart updated successfully');
      } catch (chartError) {
        console.error('Chart update error:', chartError);
        // Try to reinitialize the chart if update fails
        initializePriceChart();
      }
    } else {
      console.log('No candle data available yet');
    }
  } catch (error) {
    console.error('Error updating price chart:', error);
  }
}

// Update portfolio balance chart
async function updatePortfolioChart() {
  if (!portfolioChart) {
    console.warn('Portfolio chart not initialized, skipping update');
    return;
  }

  try {
    // Portfolio chart always shows last 24 hours regardless of price chart period
    const response = await fetch(`/api/portfolio/balance-history?period=1d`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.balanceHistory && data.balanceHistory.length > 0) {
      const totalValueData = data.balanceHistory.map(point => ({
        x: point.timestamp,
        y: point.totalValue
      }));

      const cashBalanceData = data.balanceHistory.map(point => ({
        x: point.timestamp,
        y: point.cashBalance
      }));

      const holdingsValueData = data.balanceHistory.map(point => ({
        x: point.timestamp,
        y: point.holdingsValue
      }));

      portfolioChart.data.datasets[0].data = totalValueData;
      portfolioChart.data.datasets[1].data = cashBalanceData;
      portfolioChart.data.datasets[2].data = holdingsValueData;
      portfolioChart.update('none');

      // Update balance summary
      updateBalanceSummary(data);
    }
  } catch (error) {
    console.error('Error updating portfolio chart:', error);
  }
}

// Update balance summary display
function updateBalanceSummary(data) {
  const cashElement = document.getElementById('cashBalance');
  const holdingsElement = document.getElementById('holdingsValue');
  const totalElement = document.getElementById('totalValue');
  const pnlElement = document.getElementById('pnlDisplay');

  if (cashElement) cashElement.textContent = `$${(data.currentBalance || 0).toFixed(2)}`;
  if (holdingsElement) holdingsElement.textContent = `$${(data.currentBtcValue || data.holdingsValue || 0).toFixed(2)}`;
  if (totalElement) totalElement.textContent = `$${(data.totalValue || 0).toFixed(2)}`;

  // Use the same P&L calculation as the main P&L card for consistency
  if (pnlElement && data.pnl !== undefined) {
    const pnlValue = parseFloat(data.pnl);
    const startingBalance = 100; // Should match the starting balance used elsewhere
    const pnlPercent = startingBalance > 0 ? (pnlValue / startingBalance) * 100 : 0;
    pnlElement.textContent = `P&L: ${pnlValue >= 0 ? '+' : ''}$${pnlValue.toFixed(2)} (${pnlPercent >= 0 ? '+' : ''}${pnlPercent.toFixed(1)}%)`;

    // Update color based on P&L
    pnlElement.classList.remove('positive', 'negative');
    if (pnlValue > 0) {
      pnlElement.classList.add('positive');
    } else if (pnlValue < 0) {
      pnlElement.classList.add('negative');
    }
  }
}

// Legacy function for backward compatibility
function updateChart(price, timestamp = new Date()) {
  // This function is kept for backward compatibility
  // The new system uses updateCharts() which fetches OHLC data
  console.log('Legacy updateChart called, consider using updateCharts() instead');
}

// Add a trade event marker to the chart
function addTradeEvent(type, price, timestamp = new Date()) {
  tradeEvents.push({
    type: type,
    price: parseFloat(price),
    timestamp: timestamp
  });

  console.log(`Added ${type} event at $${price}`);

  updateTradeEventMarkers();
  if (priceChart) {
    priceChart.update('none');
  }
}

// Update trade event markers based on current timeframe
function updateTradeEventMarkers() {
  if (!priceChart) return;

  priceChart.options.plugins.annotation.annotations = {};

  const filteredEvents = filterEventsByTimeframe(tradeEvents, currentTimeframe);

  filteredEvents.forEach((event, index) => {
    priceChart.options.plugins.annotation.annotations[`event${index}`] = {
      type: 'point',
      xValue: event.timestamp,
      yValue: event.price,
      backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
      borderColor: '#ffffff',
      borderWidth: 2,
      radius: 6,
      label: {
        enabled: true,
        content: event.type === 'buy' ? '↑ BUY' : '↓ SELL',
        position: event.type === 'buy' ? 'bottom' : 'top',
        backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
        color: '#000000',
        font: {
          weight: 'bold',
          size: 11
        },
        padding: 4,
        yAdjust: event.type === 'buy' ? -8 : 8
      }
    };
  });
}

// Filter data based on timeframe
function filterDataByTimeframe(data, timeframe) {
  if (!data.length) return [];

  const now = new Date();
  let cutoff;

  switch (timeframe) {
    case '1d':
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '1w':
      cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '1m':
      cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '3m':
      cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  return data.filter(point => new Date(point.x) >= cutoff);
}

// Filter events based on timeframe
function filterEventsByTimeframe(events, timeframe) {
  if (!events.length) return [];

  const now = new Date();
  let cutoff;

  switch (timeframe) {
    case '1d':
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '1w':
      cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '1m':
      cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '3m':
      cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  return events.filter(event => new Date(event.timestamp) >= cutoff);
}

// Update chart timeframe
function updateChartTimeframe(timeframe) {
  if (!priceChart) return;

  priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, timeframe);
  updateTradeEventMarkers();
  priceChart.update();
}

// WebSocket connection - use same port as current page
const ws = new WebSocket('ws://' + location.hostname + ':' + location.port);

ws.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    botRunning = data.botRunning;

    // Handle multi-symbol price data
    if (data.symbolPrices) {
      // Update global symbolPrices object
      Object.keys(data.symbolPrices).forEach(symbol => {
        symbolPrices[symbol] = data.symbolPrices[symbol];
      });

      // Update market summary ticker
      updateMarketSummary();
      updateSymbolButtonPrices();

      console.log(`Updated prices for ${Object.keys(data.symbolPrices).length} symbols`);
    }

    // Handle multi-symbol holdings data
    if (data.multiSymbolHoldings) {
      Object.keys(data.multiSymbolHoldings).forEach(symbol => {
        const holdingData = data.multiSymbolHoldings[symbol];
        const priceData = symbolPrices[symbol];
        const price = priceData ? priceData.price : 0;
        const value = holdingData.quantity * price;

        updateSymbolHoldings(symbol, holdingData.quantity, value);
      });

      console.log(`Updated holdings for ${Object.keys(data.multiSymbolHoldings).length} symbols`);

      // Ensure card visibility is updated after all holdings are processed
      updateHoldingsCardVisibility();
    }

    // Handle historical prices on initial connection
    if (data.historicalPrices && data.historicalPrices.length > 0) {
      console.log(`Received ${data.historicalPrices.length} historical price points`);

      priceHistory = [];

      data.historicalPrices.forEach(point => {
        priceHistory.push({
          x: new Date(point.timestamp),
          y: point.price
        });
      });

      console.log(`Loaded ${priceHistory.length} price points into chart`);
      // Note: Chart updates are now handled by the candlestick system via updateCharts()
    }

    // Handle new single price point updates
    if (data.newPricePoint) {
      const newPoint = {
        x: new Date(data.newPricePoint.timestamp),
        y: data.newPricePoint.price
      };

      priceHistory.push(newPoint);

      if (priceHistory.length > 1000) {
        priceHistory = priceHistory.slice(-1000);
      }

      // Note: Chart updates are now handled by the candlestick system via updateCharts()
      // The candlestick aggregator will automatically update when new prices come in
    }

    // Fallback: Update chart with current price if available
    else if (data.btcPrice && !data.historicalPrices) {
      const price = parseFloat(data.btcPrice);
      if (price > 0) {
        console.log(`Received BTC price: $${price}`);
        updateChart(price);
      }
    }

    // Check for trade events in logs
    if (data.logs && data.logs.length) {
      const lastLog = data.logs[data.logs.length - 1];

      if (lastLog.includes('BUY order placed:') && data.btcPrice) {
        addTradeEvent('buy', data.btcPrice);
      }

      if (lastLog.includes('SELL order placed:') && data.btcPrice) {
        addTradeEvent('sell', data.btcPrice);
      }
    }



    // Update financial data
    if (data.balance) {
      balanceEl.textContent = `$${parseFloat(data.balance).toFixed(2)}`;
    }

    if (data.cryptoHoldings) {
      const holdings = parseFloat(data.cryptoHoldings);
      // Update legacy element for backward compatibility
      if (cryptoHoldingsEl) {
        cryptoHoldingsEl.textContent = holdings.toFixed(8);
      }

      // Update current symbol holdings card
      const price = data.cryptoPrice ? parseFloat(data.cryptoPrice) : 0;
      const value = holdings * price;
      updateSymbolHoldings(currentSymbol, holdings, value);
    }

    if (data.cryptoPrice) {
      const price = parseFloat(data.cryptoPrice);

      // Update enhanced symbol selector with current price
      // Calculate change percentage (placeholder - would need previous price)
      const change = 0; // TODO: Calculate actual change percentage
      updateCurrentSymbolPrice(currentSymbol, price, change);
    }

    if (data.portfolioValue) {
      portfolioValueEl.textContent = `$${parseFloat(data.portfolioValue).toFixed(2)}`;
    }

    if (data.pnl !== undefined) {
      const pnlValue = parseFloat(data.pnl);
      pnlEl.textContent = `${pnlValue >= 0 ? '+' : ''}$${pnlValue.toFixed(2)}`;
      pnlEl.className = `stat-value ${pnlValue >= 0 ? 'positive' : 'negative'}`;

      // Also update the portfolio balance P&L display for consistency
      const pnlElement = document.getElementById('pnlDisplay');
      if (pnlElement) {
        const startingBalance = 100; // Should match the starting balance used elsewhere
        const pnlPercent = startingBalance > 0 ? (pnlValue / startingBalance) * 100 : 0;
        pnlElement.textContent = `P&L: ${pnlValue >= 0 ? '+' : ''}$${pnlValue.toFixed(2)} (${pnlPercent >= 0 ? '+' : ''}${pnlPercent.toFixed(1)}%)`;

        // Update color based on P&L
        pnlElement.classList.remove('positive', 'negative');
        if (pnlValue > 0) {
          pnlElement.classList.add('positive');
        } else if (pnlValue < 0) {
          pnlElement.classList.add('negative');
        }
      }
    }



    // Update position modal if it's open
    if (currentModalStrategy && data.symbolPrices) {
      updatePositionModalPrices(data.symbolPrices);
    }

    // Update available balance for slider
    if (data.balance) {
      console.log(`📊 Status update: received balance $${data.balance}`);
      updateBalanceSlider(data.balance);
    } else {
      console.log(`⚠️ Status update: no balance data received`);
    }

    // Update strategy status if available
    if (data.strategyStatus && data.strategyStatus.strategies) {
      updateStrategyStatusDisplay(data.strategyStatus);
    }

    // Update logs
    if (data.logs && data.logs.length) {
      logEl.innerHTML = data.logs.slice(-100).map(l => `<div class="log-entry">${l}</div>`).join('');
      logEl.scrollTop = logEl.scrollHeight;
    }


  } catch (error) {
    console.error('Error processing WebSocket message:', error);
  }
};

ws.onopen = () => {
  console.log('WebSocket connected');
};

ws.onclose = () => {
  console.log('WebSocket disconnected');
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

// Event listeners - add null checks
console.log('📝 Setting up DOMContentLoaded listener...');
document.addEventListener('DOMContentLoaded', async function () {
  console.log('🎯 DOM loaded, initializing...');

  // Load active symbols first
  console.log('🔧 Loading active symbols...');
  await loadActiveSymbols();

  console.log('🔧 About to call initializeChart()');
  initializeChart();
  console.log('🔧 About to call initializePage()');
  initializePage();
  console.log('🔧 About to load strategies...');
  await loadStrategies();
  console.log('🔧 Loading symbol allocations...');
  await loadSymbolAllocations();
  console.log('🔧 Initializing enhanced UI...');
  updateEnhancedSymbolSelector();
  updateMarketSummary();
  updateHoldingsCardVisibility();
  console.log('✅ Initialization complete');
});

// Initialize DOM elements
function initializeDOMElements() {
  console.log('🔗 Initializing DOM elements...');
  balanceEl = document.getElementById('balance');
  cryptoHoldingsEl = document.getElementById('cryptoHoldings');
  portfolioValueEl = document.getElementById('portfolioValue');
  pnlEl = document.getElementById('pnl');
  logEl = document.getElementById('log');

  // Symbol selector elements
  symbolSelect = document.getElementById('symbolSelect');
  holdingsTitleEl = document.getElementById('holdingsTitle');
  priceTitleEl = document.getElementById('priceTitle');
  chartTitleEl = document.getElementById('chartTitle');

  // Enhanced symbol selector elements
  stickyMarketTicker = document.getElementById('stickyMarketTicker');

  // Initialize holdings elements references
  Object.keys(symbolConfig).forEach(symbol => {
    const config = symbolConfig[symbol];
    const code = config.code.toLowerCase();
    holdingsElements[symbol] = {
      card: document.getElementById(`${code}-holdings-card`),
      holdings: document.getElementById(`${code}-holdings`),
      value: document.getElementById(`${code}-value`)
    };
  });

  strategySelect = document.getElementById('strategySelect');
  applyStrategyBtn = document.getElementById('applyStrategy');
  maxBalanceSlider = document.getElementById('maxBalanceSlider');
  maxBalanceValue = document.getElementById('maxBalanceValue');
  availableBalanceEl = document.getElementById('availableBalance');
  activeStrategiesEl = document.getElementById('activeStrategies');
  lastRebalanceEl = document.getElementById('lastRebalance');
  rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');
  nextRebalanceEl = document.getElementById('nextRebalance');
  rebalanceBtn = document.getElementById('rebalanceBtn');
  console.log('✅ DOM elements initialized');

  // Add symbol selector event listener
  if (symbolSelect) {
    symbolSelect.addEventListener('change', handleSymbolChange);
    console.log('✅ Symbol selector event listener added');
  }

  // Add quick symbol switcher event listeners
  const symbolButtons = document.querySelectorAll('.symbol-btn');
  symbolButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const symbol = btn.getAttribute('data-symbol');
      if (symbol && symbol !== currentSymbol) {
        // Update dropdown
        symbolSelect.value = symbol;
        // Trigger change event
        handleSymbolChange();
        // Update button states
        updateSymbolButtonStates(symbol);
      }
    });
  });
  console.log('✅ Quick symbol switcher event listeners added');
}

// Load available symbols from server
async function loadAvailableSymbols() {
  try {
    console.log('Loading available symbols...');
    const response = await fetch('/api/symbols');

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const symbols = await response.json();
    console.log('Available symbols:', symbols);

    availableSymbols = symbols;

    // Populate symbol dropdown
    if (symbolSelect) {
      symbolSelect.innerHTML = '';

      // Handle both array and object responses
      const symbolArray = Array.isArray(symbols) ? symbols : symbols.symbols || [];

      if (symbolArray.length === 0) {
        // Add default symbols if none received
        const defaultSymbols = [
          { value: 'BTC-USD', label: 'Bitcoin (BTC-USD)' },
          { value: 'ETH-USD', label: 'Ethereum (ETH-USD)' },
          { value: 'DOGE-USD', label: 'Dogecoin (DOGE-USD)' },
          { value: 'LTC-USD', label: 'Litecoin (LTC-USD)' },
          { value: 'BCH-USD', label: 'Bitcoin Cash (BCH-USD)' },
          { value: 'XRP-USD', label: 'XRP (XRP-USD)' }
        ];

        defaultSymbols.forEach(symbol => {
          const option = document.createElement('option');
          option.value = symbol.value;
          option.textContent = symbol.label;
          symbolSelect.appendChild(option);
        });

        console.log('✅ Default symbols added to dropdown');
      } else {
        symbolArray.forEach(symbol => {
          const option = document.createElement('option');
          option.value = symbol.value;
          option.textContent = symbol.label;
          symbolSelect.appendChild(option);
        });

        console.log(`✅ ${symbolArray.length} symbols added to dropdown`);
      }

      // Set current symbol
      symbolSelect.value = currentSymbol;
      console.log('✅ Symbol dropdown populated');
    }

    // Update UI labels for current symbol
    updateUILabels();

  } catch (error) {
    console.error('Failed to load symbols:', error);

    // Add default symbols as fallback
    if (symbolSelect) {
      symbolSelect.innerHTML = '';
      const defaultSymbols = [
        { value: 'BTC-USD', label: 'Bitcoin (BTC-USD)' },
        { value: 'ETH-USD', label: 'Ethereum (ETH-USD)' },
        { value: 'DOGE-USD', label: 'Dogecoin (DOGE-USD)' },
        { value: 'LTC-USD', label: 'Litecoin (LTC-USD)' },
        { value: 'BCH-USD', label: 'Bitcoin Cash (BCH-USD)' },
        { value: 'XRP-USD', label: 'XRP (XRP-USD)' }
      ];

      defaultSymbols.forEach(symbol => {
        const option = document.createElement('option');
        option.value = symbol.value;
        option.textContent = symbol.label;
        symbolSelect.appendChild(option);
      });

      symbolSelect.value = currentSymbol;
      console.log('✅ Fallback symbols added to dropdown');
    }
  }
}

// Initialize page on load
async function initializePage() {
  console.log('Initializing page...');
  initializeDOMElements();
  await loadAvailableSymbols();
  await loadPortfolioStrategies();
  await loadSavedSettings();

  // Setup event listeners after DOM elements are initialized
  setupEventListeners();

  console.log('Page initialization complete');
}

// Setup event listeners
function setupEventListeners() {
  console.log('🎮 Setting up event listeners...');

  // Bot control removed - bot now auto-starts and always runs
  // Trading status is display-only

  // Rebalance allocations button
  if (rebalanceBtn) {
    rebalanceBtn.addEventListener('click', async () => {
      try {
        rebalanceBtn.disabled = true;
        rebalanceBtn.textContent = '⏳ Rebalancing...';

        const response = await fetch('/api/portfolio/rebalance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        const result = await response.json();

        if (result.success) {
          rebalanceBtn.textContent = '✅ Fixed!';
          setTimeout(() => {
            rebalanceBtn.textContent = '⚖️ Fix Allocations';
            rebalanceBtn.disabled = false;
          }, 2000);

          // Refresh the page to show updated allocations
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          rebalanceBtn.textContent = '❌ Failed';
          setTimeout(() => {
            rebalanceBtn.textContent = '⚖️ Fix Allocations';
            rebalanceBtn.disabled = false;
          }, 2000);
        }
      } catch (error) {
        console.error('Rebalance failed:', error);
        rebalanceBtn.textContent = '❌ Error';
        setTimeout(() => {
          rebalanceBtn.textContent = '⚖️ Fix Allocations';
          rebalanceBtn.disabled = false;
        }, 2000);
      }
    });
  }

  if (maxBalanceSlider && maxBalanceValue) {
    maxBalanceSlider.addEventListener('input', (e) => {
      maxTradingBalance = parseFloat(e.target.value);
      maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
      updateMaxTradingBalance(maxTradingBalance);
    });

    // Initialize slider value
    maxBalanceValue.textContent = maxBalanceSlider.value;
  }

  // Rebalance frequency slider
  const rebalanceSlider = document.getElementById('rebalanceSlider');
  const rebalanceValue = document.getElementById('rebalanceValue');

  if (rebalanceSlider && rebalanceValue) {
    rebalanceSlider.addEventListener('input', (e) => {
      rebalanceFrequency = parseFloat(e.target.value);
      rebalanceValue.textContent = rebalanceFrequency.toFixed(0);
      updateRebalanceFrequency(rebalanceFrequency);
    });

    // Initialize slider value
    rebalanceValue.textContent = rebalanceSlider.value;
  }

  console.log('✅ Event listeners setup complete');
}

// Fetch and update portfolio data
async function updatePortfolioData() {
  try {
    const response = await fetch('/api/portfolio');
    if (response.ok) {
      const data = await response.json();
      updatePortfolioDisplay(data);

      // Update charts with latest data
      await updateCharts();
    } else {
      console.error('Portfolio API response not ok:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
  }
}

// Update portfolio display - updates existing strategy cards instead of replacing them
function updatePortfolioDisplay(data) {

  // Update overview stats
  const activeStrategiesEl = document.getElementById('activeStrategies');
  const lastRebalanceEl = document.getElementById('lastRebalance');
  const rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');

  if (activeStrategiesEl) activeStrategiesEl.textContent = Object.keys(data.strategies).length;
  if (lastRebalanceEl) lastRebalanceEl.textContent = new Date(data.lastRebalance).toLocaleString();
  if (rebalanceFrequencyEl) rebalanceFrequencyEl.textContent = data.rebalanceFrequency;

  // Update next rebalance countdown
  if (data.lastRebalance && data.rebalanceFrequency) {
    // Convert frequency from hours to milliseconds
    const frequencyHours = parseFloat(data.rebalanceFrequency.replace(' hours', ''));
    const frequencyMs = frequencyHours * 60 * 60 * 1000;
    updateNextRebalanceCountdown(data.lastRebalance, frequencyMs);
  }

  // Update existing strategy cards instead of replacing them
  const strategiesGrid = document.getElementById('strategiesGrid');
  if (!strategiesGrid) {
    console.error('strategiesGrid element not found!');
    return;
  }

  // Update allocation badges and stats in existing strategy control cards
  const effectiveBalance = parseFloat(data.totalBalance || 0);
  const actualBalance = parseFloat(data.actualBalance || 0);
  const isLimited = data.maxTradingBalance !== null && actualBalance > data.maxTradingBalance;

  Object.entries(data.strategies).forEach(([strategyName, stats]) => {
    // Find the strategy card by its data attribute (much more reliable)
    const strategyCard = document.querySelector(`.strategy-control-card[data-strategy-id="${strategyName}"]`);

    if (strategyCard) {
      // Update underfunded styling
      const isUnderfunded = stats.status?.isUnderfunded || false;
      console.log(`💰 ${strategyName}: isUnderfunded=${isUnderfunded}, status=`, stats.status);
      if (isUnderfunded) {
        console.log(`🔴 Adding underfunded class to ${strategyName}`);
        strategyCard.classList.add('underfunded');
      } else {
        strategyCard.classList.remove('underfunded');
      }
      // Update allocation badge
      const allocationBadge = strategyCard.querySelector('.allocation-badge');
      if (allocationBadge) {
        allocationBadge.textContent = stats.allocation;
      }

      // Calculate and update bucket size using available balance from backend
      let bucketSize = 0;
      if (stats.availableBalance) {
        bucketSize = parseFloat(stats.availableBalance);
      } else {
        // Fallback to allocation-based calculation
        const rawAllocation = data.allocations ? data.allocations[strategyName] : 0;
        bucketSize = effectiveBalance * rawAllocation;
      }

      // Update stats section (bucket size and performance stats)
      const statsSection = strategyCard.querySelector('.strategy-stats');
      if (statsSection) {
        // Find stats by their labels instead of position (more reliable)
        const statRows = statsSection.querySelectorAll('.stat-row');

        statRows.forEach(row => {
          const label = row.querySelector('span:first-child')?.textContent;
          const valueEl = row.querySelector('span:last-child');

          if (label && valueEl) {
            switch (label) {
              case 'Bucket Size:':
                valueEl.textContent = `$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}`;
                break;
              case 'Total Return:':
                valueEl.textContent = stats.totalReturn;
                valueEl.className = parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative';
                break;
              case 'Win Rate:':
                valueEl.textContent = stats.winRate;
                break;
              case 'Trades:':
                valueEl.textContent = stats.trades;
                break;
              case 'Sharpe Ratio:':
                valueEl.textContent = stats.sharpeRatio;
                break;
            }
          }
        });
      }
    } else {
      // Strategy cards don't exist on this page - this is normal
      console.debug(`Strategy card not found: ${strategyName} (not needed on this page)`);
    }
  });
}

// Strategy Configuration Modal Functions
let currentConfigStrategy = null;

async function openStrategyConfig(strategyId) {
  try {
    currentConfigStrategy = strategyId;

    // Debug: Check what's in availableStrategies
    console.log('Available strategies:', Object.keys(availableStrategies));
    console.log('Looking for strategy:', strategyId);

    // Get strategy details
    const strategy = availableStrategies[strategyId];
    if (!strategy) {
      console.error('Strategy not found:', strategyId);
      console.log('Available strategies are:', availableStrategies);
      return;
    }

    // Get current configuration from portfolio
    const portfolioResponse = await fetch('/api/portfolio/status');
    const portfolioData = await portfolioResponse.json();
    const currentConfig = portfolioData.strategies[strategyId]?.config || {};

    // Update modal content
    document.getElementById('configModalTitle').textContent = `Configure ${strategy.displayName}`;
    document.getElementById('configModalDescription').textContent = strategy.description;

    // Generate config fields
    generateConfigFields(strategy.config, currentConfig);

    // Show modal
    document.getElementById('strategyConfigModal').classList.add('show');

  } catch (error) {
    console.error('Error opening strategy config:', error);
    await showAlert('Failed to load strategy configuration', 'Error', 'error');
  }
}

function closeStrategyConfig() {
  document.getElementById('strategyConfigModal').classList.remove('show');
  currentConfigStrategy = null;
}

function generateConfigFields(schema, currentConfig) {
  const fieldsContainer = document.getElementById('configFields');
  fieldsContainer.innerHTML = '';

  Object.entries(schema).forEach(([key, fieldConfig]) => {
    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'config-field';

    const currentValue = currentConfig[key] !== undefined ? currentConfig[key] : fieldConfig.default;

    let inputHtml = '';

    switch (fieldConfig.type) {
      case 'number':
        inputHtml = `
          <input
            type="number"
            id="config_${key}"
            name="${key}"
            value="${currentValue || ''}"
            min="${fieldConfig.min || ''}"
            max="${fieldConfig.max || ''}"
            step="${fieldConfig.step || 'any'}"
            required
          >
        `;
        break;

      case 'boolean':
        inputHtml = `
          <div class="checkbox-wrapper">
            <input
              type="checkbox"
              id="config_${key}"
              name="${key}"
              ${currentValue ? 'checked' : ''}
            >
            <label for="config_${key}">${fieldConfig.label}</label>
          </div>
        `;
        break;

      case 'select':
        const options = fieldConfig.options || [];
        inputHtml = `
          <select id="config_${key}" name="${key}" required>
            ${options.map(option =>
          `<option value="${option.value}" ${currentValue === option.value ? 'selected' : ''}>${option.label}</option>`
        ).join('')}
          </select>
        `;
        break;

      default:
        inputHtml = `
          <input
            type="text"
            id="config_${key}"
            name="${key}"
            value="${currentValue || ''}"
            required
          >
        `;
    }

    fieldDiv.innerHTML = `
      ${fieldConfig.type !== 'boolean' ? `<label for="config_${key}">${fieldConfig.label}</label>` : ''}
      ${inputHtml}
      ${fieldConfig.description ? `<div class="field-description">${fieldConfig.description}</div>` : ''}
    `;

    fieldsContainer.appendChild(fieldDiv);
  });
}

async function resetStrategyConfig() {
  if (!currentConfigStrategy) return;

  const strategy = availableStrategies[currentConfigStrategy];
  if (!strategy) return;

  // Reset all fields to default values
  Object.entries(strategy.config).forEach(([key, fieldConfig]) => {
    const field = document.getElementById(`config_${key}`);
    if (field) {
      if (fieldConfig.type === 'boolean') {
        field.checked = fieldConfig.default || false;
      } else {
        field.value = fieldConfig.default || '';
      }
    }
  });
}

// Handle form submission
document.addEventListener('DOMContentLoaded', () => {
  const form = document.getElementById('strategyConfigForm');
  if (form) {
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await saveStrategyConfig();
    });
  }
});

async function saveStrategyConfig() {
  if (!currentConfigStrategy) return;

  try {
    const formData = new FormData(document.getElementById('strategyConfigForm'));
    const config = {};

    // Get strategy schema for type conversion
    const strategy = availableStrategies[currentConfigStrategy];
    const schema = strategy.config;

    // Convert form data to proper types
    for (const [key, value] of formData.entries()) {
      const fieldConfig = schema[key];
      if (fieldConfig) {
        switch (fieldConfig.type) {
          case 'number':
            config[key] = parseFloat(value);
            break;
          case 'boolean':
            config[key] = true; // Checkbox is only in formData if checked
            break;
          default:
            config[key] = value;
        }
      }
    }

    // Handle unchecked checkboxes
    Object.entries(schema).forEach(([key, fieldConfig]) => {
      if (fieldConfig.type === 'boolean' && config[key] === undefined) {
        config[key] = false;
      }
    });

    // Send update request
    const response = await fetch('/api/portfolio/strategy/config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        strategyName: currentConfigStrategy,
        config
      })
    });

    if (response.ok) {
      closeStrategyConfig();
      // Refresh portfolio data to show updated config
      await updatePortfolioData();
      await showAlert('Strategy configuration updated successfully!', 'Success', 'success');
    } else {
      const error = await response.json();
      await showAlert(`Failed to update configuration: ${error.error}`, 'Error', 'error');
    }

  } catch (error) {
    console.error('Error saving strategy config:', error);
    await showAlert('Failed to save configuration', 'Error', 'error');
  }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
  const modal = document.getElementById('strategyConfigModal');
  if (e.target === modal) {
    closeStrategyConfig();
  }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape') {
    closeStrategyConfig();
  }
});

// Call updatePortfolioData on page load
document.addEventListener('DOMContentLoaded', () => {
  updatePortfolioData();
});

// Set interval to update portfolio data every 30 seconds
setInterval(updatePortfolioData, 30000);

// Update status function - simplified to just update portfolio data
async function updateStatus() {
  try {
    // Update portfolio data for current symbol
    await updatePortfolioData();
    console.log(`✅ Status updated for ${currentSymbol}`);
  } catch (error) {
    console.error('Error updating status:', error);
    // Don't retry immediately to avoid spam
    setTimeout(() => {
      console.log('Retrying status update...');
      updateStatus();
    }, 5000);
  }
}

// Load and display all available strategies with portfolio data
async function loadPortfolioStrategies() {
  try {
    // Stop any existing status rotations before loading new data
    stopAllStatusRotations();

    // Load available strategies
    const strategiesResponse = await fetch('/api/strategies');

    if (!strategiesResponse.ok) {
      throw new Error(`HTTP ${strategiesResponse.status}: ${strategiesResponse.statusText}`);
    }

    const strategiesData = await strategiesResponse.json();

    // Load current portfolio data with status information
    const portfolioResponse = await fetch('/api/portfolio');
    const portfolioData = portfolioResponse.ok ? await portfolioResponse.json() : { strategies: {}, allocations: {} };

    displayStrategiesGrid(strategiesData.strategies, portfolioData);

  } catch (error) {
    console.error('Error loading portfolio strategies:', error);
  }
}

// Display strategies grid with controls
function displayStrategiesGrid(availableStrategies, portfolioData) {
  const strategiesGrid = document.getElementById('strategiesGrid');
  strategiesGrid.innerHTML = '';

  // Reset strategy decision tracking when cards are recreated
  previousStrategyDecisions = {};

  let activeCount = 0;
  const effectiveBalance = parseFloat(portfolioData.totalBalance || 0);
  const actualBalance = parseFloat(portfolioData.actualBalance || 0);
  const isLimited = portfolioData.maxTradingBalance !== null && actualBalance > portfolioData.maxTradingBalance;

  availableStrategies.forEach(strategy => {
    const isActive = portfolioData.strategies && portfolioData.strategies[strategy.id];
    const stats = isActive ? portfolioData.strategies[strategy.id] : null;
    const allocation = portfolioData.allocations ? portfolioData.allocations[strategy.id] : 0;

    if (isActive) activeCount++;

    // Use available balance from backend (accurate calculation)
    let bucketSize = 0;
    if (stats?.availableBalance) {
      bucketSize = parseFloat(stats.availableBalance);
    } else {
      // Fallback: Extract from status message
      if (stats?.status?.lastDecisionReason) {
        const match = stats.status.lastDecisionReason.match(/Insufficient allocation: \$([0-9.]+)/);
        if (match) {
          bucketSize = parseFloat(match[1]);
        }
      }

      // Last resort: Calculate from total balance (less accurate)
      if (bucketSize === 0) {
        const allocationPercent = stats ? parseFloat(stats.allocation.replace('%', '')) / 100 : 0;
        const portfolioEffectiveBalance = parseFloat(portfolioData.totalBalance || effectiveBalance || 90);
        bucketSize = portfolioEffectiveBalance * allocationPercent;
      }
    }
    const isUnderfunded = stats?.status?.isUnderfunded || false;

    // Debug underfunded calculations
    if (isUnderfunded) {
      console.log(`🔴 ${strategy.id} underfunded debug:`, {
        extractedFromStatus: bucketSize,
        requiredIncrease: stats.status?.requiredIncrease,
        statusMessage: stats.status?.lastDecisionReason
      });
    }

    const strategyCard = document.createElement('div');
    let cardClasses = `strategy-control-card ${isActive ? 'active' : 'inactive'}`;
    if (isUnderfunded) {
      cardClasses += ' underfunded';
    }
    strategyCard.className = cardClasses;
    strategyCard.setAttribute('data-strategy-id', strategy.id);
    strategyCard.setAttribute('data-strategy-id', strategy.id);
    strategyCard.innerHTML = `
      <div class="strategy-header">
        <div class="strategy-title">
          <h4>${strategy.displayName}</h4>
          <div class="strategy-controls">
            <button class="config-btn" onclick="openStrategyConfig('${strategy.id}')" title="Configure Strategy">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
              </svg>
            </button>
            <button class="history-btn" onclick="openTradeHistory('${strategy.id}')" title="View Trade History">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3v5h5"></path>
                <path d="M3.05 13A9 9 0 1 0 6 5.3L3 8"></path>
                <path d="M12 7v5l4 2"></path>
              </svg>
            </button>
            <div class="strategy-toggle">
              <label class="toggle-switch">
                <input type="checkbox" ${isActive ? 'checked' : ''}
                     onchange="toggleStrategy('${strategy.id}', this.checked)">
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
        ${isActive ? `<div class="allocation-badge">${(allocation * 100).toFixed(1)}%</div>` : ''}
      </div>
      
      <div class="strategy-description">
        <p>${strategy.description}</p>
      </div>
      
      ${isActive ? `
        <div class="strategy-status">
          <div class="status-section">
            <h5>Current Status</h5>
            <div class="status-item">
              <span class="status-label">Last Decision:</span>
              <span class="status-value ${stats.status?.lastDecision ? 'positive' : 'neutral'}">${stats.status?.lastDecision ? 'ENTER' : 'WAIT'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Reason:</span>
              <span class="status-reason">${stats.status?.lastDecisionReason || 'No recent decisions'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Next Signal:</span>
              <span class="status-next">${stats.status?.nextSignal || 'Monitoring market'}</span>
            </div>
            ${stats.status?.symbolStatuses ? `
              <div class="status-item">
                <span class="status-label">Currency Status:</span>
                <div class="symbol-statuses">
                  ${Object.entries(stats.status.symbolStatuses).map(([symbol, symbolStatus]) => `
                    <div class="symbol-status-item">
                      <span class="symbol-code">${symbolStatus.assetCode}</span>
                      <span class="symbol-price">$${symbolStatus.currentPrice.toFixed(2)}</span>
                      <span class="symbol-decision ${symbolStatus.lastDecision ? 'ready' : 'waiting'}">${symbolStatus.lastDecision ? 'Ready' : 'Waiting'}</span>
                    </div>
                  `).join('')}
                </div>
              </div>
            ` : ''}
            ${stats.status?.lastTradeTime ? `
              <div class="status-item">
                <span class="status-label">Last Trade:</span>
                <span class="status-trade">${stats.status.lastTradeType?.toUpperCase()} at $${stats.status.lastTradePrice?.toFixed(2)} (${new Date(stats.status.lastTradeTime).toLocaleString()})</span>
              </div>
            ` : ''}
          </div>
        </div>

        ${stats.position?.hasPosition || stats.orders?.activeStopLoss || (stats.orders?.pendingOrders && stats.orders.pendingOrders.length > 0) ? `
          <div class="trading-metrics">
            <h5>Trading Position</h5>
            ${stats.position?.hasPosition ? `
              <div class="position-info">
                ${stats.position.positionCount > 1 ? `
                  <div class="multi-currency-summary">
                    <div class="stat-row">
                      <span>Total Positions:</span>
                      <span class="clickable-positions" onclick="showPositionDetails('${strategy.id}')" style="cursor: pointer; color: #007bff; text-decoration: underline;">
                        ${stats.position.positionCount} currencies
                      </span>
                    </div>
                    <div class="stat-row">
                      <span>Total Value:</span>
                      <span>$${parseFloat(stats.position.totalValue || 0).toFixed(2)}</span>
                    </div>
                    <div class="stat-row">
                      <span>Total Cost:</span>
                      <span>$${parseFloat(stats.position.totalCost || 0).toFixed(2)}</span>
                    </div>
                    <div class="stat-row">
                      <span>Total Unrealized P&L:</span>
                      <span class="${parseFloat(stats.position.totalUnrealizedPnL || 0) >= 0 ? 'positive' : 'negative'}">
                        ${parseFloat(stats.position.totalUnrealizedPnL || 0) >= 0 ? '+' : ''}$${parseFloat(stats.position.totalUnrealizedPnL || 0).toFixed(2)}
                      </span>
                    </div>
                    <div class="stat-row">
                      <span>Total Realized P&L:</span>
                      <span class="${parseFloat(stats.position.totalRealizedPnL || 0) >= 0 ? 'positive' : 'negative'}">
                        ${parseFloat(stats.position.totalRealizedPnL || 0) >= 0 ? '+' : ''}$${parseFloat(stats.position.totalRealizedPnL || 0).toFixed(2)}
                      </span>
                    </div>
                  </div>
                ` : `
                  <div class="single-currency-position">
                    <div class="stat-row">
                      <span>Positions:</span>
                      <span class="clickable-positions" onclick="showPositionDetails('${strategy.id}')" style="cursor: pointer; color: #007bff; text-decoration: underline;">
                        ${stats.position.positionCount || 0} currencies
                      </span>
                    </div>
                    <div class="stat-row">
                      <span>Total Value:</span>
                      <span>$${parseFloat(stats.position.totalValue || 0).toFixed(2)}</span>
                    </div>
                    <div class="stat-row">
                      <span>Total Cost:</span>
                      <span>$${parseFloat(stats.position.totalCost || 0).toFixed(2)}</span>
                    </div>
                    <div class="stat-row">
                      <span>Unrealized P&L:</span>
                      <span class="${parseFloat(stats.position.totalUnrealizedPnL || 0) >= 0 ? 'positive' : 'negative'}">
                        ${parseFloat(stats.position.totalUnrealizedPnL || 0) >= 0 ? '+' : ''}$${parseFloat(stats.position.totalUnrealizedPnL || 0).toFixed(2)}
                      </span>
                    </div>
                    <div class="stat-row">
                      <span>Total P&L:</span>
                      <span class="${parseFloat(stats.position.totalUnrealizedPnL || 0) + parseFloat(stats.position.totalRealizedPnL || 0) >= 0 ? 'positive' : 'negative'}">
                        ${parseFloat(stats.position.totalUnrealizedPnL || 0) + parseFloat(stats.position.totalRealizedPnL || 0) >= 0 ? '+' : ''}$${(parseFloat(stats.position.totalUnrealizedPnL || 0) + parseFloat(stats.position.totalRealizedPnL || 0)).toFixed(2)}
                      </span>
                    </div>
                  </div>
                `}
              </div>
            ` : ''}
            ${stats.orders?.activeStopLoss ? `
              <div class="stop-loss-info">
                <div class="stat-row">
                  <span>Stop Loss:</span>
                  <span class="stop-loss-price">$${stats.orders.activeStopLoss.stopPrice}</span>
                </div>
              </div>
            ` : ''}
            ${stats.orders?.pendingOrders && stats.orders.pendingOrders.length > 0 ? `
              <div class="pending-orders-info">
                <div class="stat-row">
                  <span>Pending Orders:</span>
                  <span class="pending-count">${stats.orders.pendingOrders.length}</span>
                </div>
                ${stats.orders.pendingOrders.map(order => `
                  <div class="pending-order-item">
                    <span class="order-type">${order.orderType.toUpperCase()} ${order.side.toUpperCase()}</span>
                    <span class="order-details">
                      ${order.quantity} BTC${order.price ? ` @ $${order.price}` : ' (Market)'}
                    </span>
                  </div>
                `).join('')}
              </div>
            ` : ''}
          </div>
        ` : ''}

        ${isUnderfunded ? `
          <div class="underfunded-warning">
            <h5>Insufficient Funding</h5>
            <p>This strategy has $${bucketSize.toFixed(2)} allocated but requires minimum $5.00 to increase position.</p>
            <p class="suggestion">Need $${(stats.status?.requiredIncrease || 0).toFixed(2)} more. Increase max trading balance or reduce number of active strategies.</p>
          </div>
        ` : ''}

        <div class="strategy-stats">
          <div class="stat-row">
            <span>Bucket Size:</span>
            <span>$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}</span>
          </div>
          <div class="stat-row">
            <span>Total Return:</span>
            <span class="${parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative'}">${stats.totalReturn}</span>
          </div>
          <div class="stat-row">
            <span>Win Rate:</span>
            <span>${stats.winRate}</span>
          </div>
          <div class="stat-row">
            <span>Trades:</span>
            <span>${stats.trades}</span>
          </div>
          <div class="stat-row">
            <span>Sharpe Ratio:</span>
            <span>${stats.sharpeRatio}</span>
          </div>
        </div>
      ` : `
        <div class="strategy-inactive">
          <p>Strategy is currently disabled</p>
        </div>
      `}
    `;
    strategiesGrid.appendChild(strategyCard);

    // Start status rotation for strategies with multiple currencies
    if (isActive && stats?.status?.symbolStatuses && Object.keys(stats.status.symbolStatuses).length > 1) {
      startStatusRotation(strategy.id, stats.status.symbolStatuses);
    }
  });

  // Update active strategies count
  document.getElementById('activeStrategies').textContent = activeCount;

  // Update other portfolio stats if available
  if (portfolioData.lastRebalance) {
    document.getElementById('lastRebalance').textContent = new Date(portfolioData.lastRebalance).toLocaleString();
  }
  if (portfolioData.rebalanceFrequency) {
    document.getElementById('rebalanceFrequency').textContent = portfolioData.rebalanceFrequency;

    // Update next rebalance countdown
    if (portfolioData.lastRebalance) {
      const frequencyHours = parseFloat(portfolioData.rebalanceFrequency.replace(' hours', ''));
      const frequencyMs = frequencyHours * 60 * 60 * 1000;
      updateNextRebalanceCountdown(portfolioData.lastRebalance, frequencyMs);
    }
  }
}

// Track previous strategy decisions to detect changes
let previousStrategyDecisions = {};

// Highlight strategy card when it makes a trading decision
function highlightStrategyCard(strategyCard, strategyName) {
  // Scroll the card into view with a small delay to ensure DOM is ready
  setTimeout(() => {
    strategyCard.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    });
  }, 100);

  // Add blinking animation class
  strategyCard.classList.add('strategy-highlight');

  // Remove the highlight after animation completes
  setTimeout(() => {
    if (strategyCard.classList.contains('strategy-highlight')) {
      strategyCard.classList.remove('strategy-highlight');
    }
  }, 3000); // 3 seconds of blinking

  console.log(`✨ Highlighted strategy card for ${strategyName} - scrolling into view and blinking`);

  // Optional: Show a brief notification (you can uncomment this if desired)
  showBriefNotification(`${strategyName} wants to trade!`);
}

// Optional notification function (currently unused but available)
function showBriefNotification(message) {
  // Create a temporary notification element
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease-out;
  `;

  document.body.appendChild(notification);

  // Remove after 2 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease-in forwards';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 2000);
}

// Update strategy status display in real-time
function updateStrategyStatusDisplay(strategyData) {
  if (!strategyData.strategies) return;

  Object.entries(strategyData.strategies).forEach(([strategyName, stats]) => {
    // Find the strategy card by its data attribute (more reliable)
    const strategyCard = document.querySelector(`.strategy-control-card[data-strategy-id="${strategyName}"]`);

    if (!strategyCard) {
      // Strategy card not found - might be disabled or not yet rendered
      return;
    }

    if (strategyCard && stats.status) {
      // Check if this strategy just made a trading decision
      const currentDecision = stats.status.lastDecision;
      const previousDecision = previousStrategyDecisions[strategyName];

      // If strategy just decided to ENTER (changed from false/undefined to true)
      if (currentDecision === true && previousDecision !== true) {
        console.log(`🎯 Strategy ${strategyName} just decided to ENTER! Previous: ${previousDecision}, Current: ${currentDecision}`);
        console.log(`📋 Decision reason: ${stats.status.lastDecisionReason}`);
        highlightStrategyCard(strategyCard, strategyName);
      }

      // Update our tracking
      previousStrategyDecisions[strategyName] = currentDecision;

      // Update underfunded styling
      const isUnderfunded = stats.status.isUnderfunded || false;
      if (isUnderfunded) {
        strategyCard.classList.add('underfunded');
      } else {
        strategyCard.classList.remove('underfunded');
      }
      // Update status section if it exists
      const statusSection = strategyCard.querySelector('.strategy-status');
      if (statusSection) {
        const lastDecisionValue = statusSection.querySelector('.status-value');
        const reasonElement = statusSection.querySelector('.status-reason');
        const nextSignalElement = statusSection.querySelector('.status-next');

        if (lastDecisionValue) {
          lastDecisionValue.textContent = stats.status.lastDecision ? 'ENTER' : 'WAIT';
          lastDecisionValue.className = `status-value ${stats.status.lastDecision ? 'positive' : 'neutral'}`;
        }

        if (reasonElement) {
          reasonElement.textContent = stats.status.lastDecisionReason || 'No recent decisions';
        }

        if (nextSignalElement) {
          nextSignalElement.textContent = stats.status.nextSignal || 'Monitoring market';
        }
      }
    }
  });
}

// Toggle strategy on/off with safety checks
async function toggleStrategy(strategyId, enabled) {
  try {
    const response = await fetch('/api/portfolio/strategy/toggle', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ strategyName: strategyId, enabled })
    });

    if (response.ok) {
      const result = await response.json();

      // Show success message with details about actions taken
      if (!enabled && result.actionsExecuted && result.actionsExecuted.length > 0) {
        await showAlert(
          `Strategy Disabled Successfully!\n\n` +
          `Strategy: ${strategyId}\n\n` +
          `Actions taken:\n${result.actionsExecuted.map(action => `• ${action}`).join('\n')}\n\n` +
          `The strategy has been removed from your portfolio and allocation redistributed.`,
          'Strategy Disabled',
          'success'
        );
      }

      // Reload the strategies display
      await loadPortfolioStrategies();
      console.log(`${enabled ? 'Enabled' : 'Disabled'} strategy: ${strategyId}`);
    } else {
      const errorData = await response.json();

      // Handle safety check failure for disable (this should rarely happen now)
      if (!enabled && errorData.canForceDisable) {
        const confirmed = await showPositionWarningDialog(strategyId, errorData);
        if (confirmed) {
          await forceDisableStrategy(strategyId, true); // Close position
        } else {
          // Reset the toggle since user cancelled
          await loadPortfolioStrategies();
        }
        return;
      }

      console.error('Failed to toggle strategy:', errorData.error);
      await showAlert(`Failed to ${enabled ? 'enable' : 'disable'} strategy: ${errorData.error}`, 'Error', 'error');
      await loadPortfolioStrategies(); // Reset toggle state
    }
  } catch (error) {
    console.error('Error toggling strategy:', error);
    await showAlert('Error toggling strategy. Please try again.', 'Error', 'error');
    // Reload to reset the toggle state
    await loadPortfolioStrategies();
  }
}

// Show warning dialog for strategies with open positions
async function showPositionWarningDialog(strategyId, errorData) {
  const warnings = errorData.warnings || [];
  const position = errorData.position || {};

  const message = `🔄 DISABLE STRATEGY & CLOSE POSITION

Strategy: "${strategyId}"

${warnings.join('\n')}

⚠️ IMPORTANT: Disabling this strategy will:
• Place a MARKET SELL order immediately
• Close the position at current market price
• Remove the strategy from your portfolio
• Redistribute allocation to remaining strategies

Positions to close: ${position.positionCount || 0} currencies (Total value: $${parseFloat(position.totalValue || 0).toFixed(2)})

Do you want to disable the strategy and close the position?`;

  return await showConfirm(message, 'Disable Strategy & Close Position');
}

// Force disable strategy with position closure
async function forceDisableStrategy(strategyId, closePosition = false) {
  try {
    const response = await fetch('/api/portfolio/strategy/force-disable', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ strategyName: strategyId, closePosition })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Failed to force disable strategy:', errorData.error);
      await showAlert(`Failed to force disable strategy: ${errorData.error}`, 'Error', 'error');
      return;
    }

    const result = await response.json();
    console.log(`Strategy ${strategyId} force disabled:`, result);

    if (result.actionsExecuted && result.actionsExecuted.length > 0) {
      const actions = result.actionsExecuted.map(a => a.reason).join('\n');
      await showAlert(`Strategy disabled successfully!\n\nActions executed:\n${actions}`, 'Success', 'success');
    }

    // Refresh the portfolio data
    await loadPortfolioStrategies();

  } catch (error) {
    console.error('Error force disabling strategy:', error);
    await showAlert('Error force disabling strategy. Please try again.', 'Error', 'error');
    await loadPortfolioStrategies();
  }
}

// Gap filling is now automatic - no manual intervention needed

// Symbol Allocation Management
async function loadSymbolAllocations() {
  try {
    console.log('📊 Loading symbol allocations from API...');
    const response = await fetch('/api/symbol-allocations');
    console.log('📊 API response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('📊 Raw API data:', data);

      symbolAllocations = data.symbolAllocations || {};
      activeSymbols = data.activeSymbols || [];

      console.log('📊 Loaded symbol allocations:', symbolAllocations);
      console.log('📊 Active symbols:', activeSymbols);

      generateAllocationSliders();
      updateAllocationDisplay();
    } else {
      console.error('📊 API response not ok:', response.status, response.statusText);
      throw new Error(`API returned ${response.status}`);
    }
  } catch (error) {
    console.error('Error loading symbol allocations:', error);
    // Use defaults if loading fails - get from active symbols or fallback
    const fallbackSymbols = activeSymbols.length > 0 ? activeSymbols : ['BTC-USD', 'ETH-USD', 'DOGE-USD', 'LTC-USD', 'BCH-USD', 'XRP-USD'];
    symbolAllocations = {};
    const allocation = 1.0 / fallbackSymbols.length;
    fallbackSymbols.forEach(symbol => {
      symbolAllocations[symbol] = allocation;
    });
    activeSymbols = fallbackSymbols;
    console.log('📊 Using fallback data:', { symbolAllocations, activeSymbols });
    generateAllocationSliders();
  }
}

// Load detailed multi-currency position data
async function loadDetailedPositions() {
  try {
    const response = await fetch('/api/portfolio/positions');
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('💰 Received detailed position data:', data);

    return data;
  } catch (error) {
    console.error('Error loading detailed positions:', error);
    return null;
  }
}

function generateAllocationSliders() {
  console.log('🎛️ generateAllocationSliders called');
  console.log('🎛️ activeSymbols:', activeSymbols);
  console.log('🎛️ symbolAllocations:', symbolAllocations);

  const container = document.getElementById('symbolAllocationSliders');
  if (!container) {
    console.error('🎛️ Container symbolAllocationSliders not found!');
    return;
  }

  console.log('🎛️ Container found, generating sliders...');
  container.innerHTML = '';
  allocationSliders = {};

  if (!activeSymbols || activeSymbols.length === 0) {
    console.warn('🎛️ No active symbols to generate sliders for');
    return;
  }

  activeSymbols.forEach(symbol => {
    const config = symbolConfig[symbol] || { icon: '?', name: symbol, code: symbol };
    const allocation = symbolAllocations[symbol] || 0;

    console.log(`🎛️ Creating slider for ${symbol}: ${allocation * 100}%`);

    const sliderHtml = `
      <div class="allocation-control" data-symbol="${symbol}">
        <div class="allocation-header">
          <div class="allocation-label">
            <span class="symbol-icon">${config.icon}</span>
            <span>${config.name}</span>
          </div>
          <span class="allocation-value" id="allocation-${symbol}">${Math.round(allocation * 100)}%</span>
        </div>
        <input
          type="range"
          id="slider-${symbol}"
          class="allocation-slider"
          min="0"
          max="100"
          value="${Math.round(allocation * 100)}"
          step="1"
          data-symbol="${symbol}"
        >
        <div class="allocation-details">
          <span>0%</span>
          <span>100%</span>
        </div>
      </div>
    `;

    container.insertAdjacentHTML('beforeend', sliderHtml);

    // Store slider reference
    const slider = document.getElementById(`slider-${symbol}`);
    allocationSliders[symbol] = slider;

    // Add event listener
    slider.addEventListener('input', (e) => handleAllocationChange(symbol, e.target.value));
  });

  console.log(`🎛️ Generated ${activeSymbols.length} allocation sliders`);
  console.log('🎛️ Container HTML:', container.innerHTML.substring(0, 200) + '...');
}

function handleAllocationChange(changedSymbol, newValue) {
  // Snap to nearest 1% increment (whole numbers)
  const snappedValue = Math.round(parseFloat(newValue));
  const newAllocation = snappedValue / 100;
  const oldAllocation = symbolAllocations[changedSymbol] || 0;
  const difference = newAllocation - oldAllocation;

  // Update the changed symbol
  symbolAllocations[changedSymbol] = newAllocation;

  // Redistribute the difference among other symbols
  const otherSymbols = activeSymbols.filter(s => s !== changedSymbol);
  const totalOtherAllocation = otherSymbols.reduce((sum, symbol) => sum + (symbolAllocations[symbol] || 0), 0);

  if (otherSymbols.length > 0 && totalOtherAllocation > 0) {
    // Proportionally adjust other symbols
    otherSymbols.forEach(symbol => {
      const currentAllocation = symbolAllocations[symbol] || 0;
      const proportion = currentAllocation / totalOtherAllocation;
      const adjustment = difference * proportion;
      symbolAllocations[symbol] = Math.max(0, Math.min(1, currentAllocation - adjustment));
    });
  } else if (otherSymbols.length > 0) {
    // If other symbols have 0 allocation, distribute evenly
    const remainingAllocation = Math.max(0, 1 - newAllocation);
    const perSymbol = remainingAllocation / otherSymbols.length;
    otherSymbols.forEach(symbol => {
      symbolAllocations[symbol] = perSymbol;
    });
  }

  // Normalize to ensure total equals 1.0
  normalizeAllocations();

  // Update UI
  updateAllocationSliders();
  updateAllocationDisplay();

  // Save to server
  saveSymbolAllocations();
}

function normalizeAllocations() {
  const total = Object.values(symbolAllocations).reduce((sum, alloc) => sum + alloc, 0);
  if (total > 0 && Math.abs(total - 1.0) > 0.001) {
    Object.keys(symbolAllocations).forEach(symbol => {
      symbolAllocations[symbol] = symbolAllocations[symbol] / total;
    });
  }
}

function updateAllocationSliders() {
  Object.keys(allocationSliders).forEach(symbol => {
    const slider = allocationSliders[symbol];
    const valueElement = document.getElementById(`allocation-${symbol}`);
    const allocation = symbolAllocations[symbol] || 0;

    if (slider) {
      slider.value = Math.round(allocation * 100);
    }
    if (valueElement) {
      valueElement.textContent = `${Math.round(allocation * 100)}%`;
    }
  });
}

function updateAllocationDisplay() {
  const totalElement = document.getElementById('allocationTotal');
  const statusElement = document.getElementById('allocationStatus');

  if (!totalElement || !statusElement) return;

  const total = Object.values(symbolAllocations).reduce((sum, alloc) => sum + alloc, 0);
  const totalPercent = total * 100;

  totalElement.textContent = Math.round(totalPercent);

  // Update status
  statusElement.className = 'allocation-status';
  if (Math.abs(total - 1.0) < 0.001) {
    statusElement.textContent = '✅ Balanced';
    statusElement.classList.add('balanced');
  } else if (Math.abs(total - 1.0) < 0.05) {
    statusElement.textContent = '⚠️ Close';
    statusElement.classList.add('unbalanced');
  } else {
    statusElement.textContent = '❌ Unbalanced';
    statusElement.classList.add('error');
  }
}

async function saveSymbolAllocations() {
  try {
    console.log('💾 Saving symbol allocations:', symbolAllocations);

    const response = await fetch('/api/symbol-allocations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ symbolAllocations })
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to save allocations: ${response.status} - ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ Symbol allocations saved successfully:', result);

    // Show brief success indicator
    const statusElement = document.getElementById('allocationStatus');
    if (statusElement) {
      statusElement.textContent = '💾 Saved!';
      setTimeout(() => {
        updateAllocationDisplay(); // Restore original status
      }, 1000);
    }

  } catch (error) {
    console.error('❌ Error saving symbol allocations:', error);
    await showAlert('Failed to save symbol allocations. Please try again.', 'Error', 'error');
  }
}

// Custom Modal System
let alertModalResolve = null;
let confirmModalResolve = null;

// Custom Alert Modal
function showAlert(message, title = 'Alert', type = 'info') {
  return new Promise((resolve) => {
    alertModalResolve = resolve;

    // Set content
    document.getElementById('alertModalTitle').textContent = title;
    const messageEl = document.getElementById('alertModalMessage');
    messageEl.textContent = message;

    // Set message type class
    messageEl.className = `alert-message ${type}`;

    // Show modal
    document.getElementById('alertModal').classList.add('show');

    // Focus the OK button
    setTimeout(() => {
      const okButton = document.querySelector('#alertModal .btn-primary');
      if (okButton) okButton.focus();
    }, 100);
  });
}

// Custom Confirm Modal
function showConfirm(message, title = 'Confirm') {
  return new Promise((resolve) => {
    confirmModalResolve = resolve;

    // Set content
    document.getElementById('confirmModalTitle').textContent = title;
    document.getElementById('confirmModalMessage').textContent = message;

    // Show modal
    document.getElementById('confirmModal').classList.add('show');

    // Focus the Cancel button by default
    setTimeout(() => {
      const cancelButton = document.querySelector('#confirmModal .btn-secondary');
      if (cancelButton) cancelButton.focus();
    }, 100);
  });
}

// Close Alert Modal
function closeAlertModal() {
  document.getElementById('alertModal').classList.remove('show');
  if (alertModalResolve) {
    alertModalResolve();
    alertModalResolve = null;
  }
}

// Close Confirm Modal
function closeConfirmModal(result) {
  document.getElementById('confirmModal').classList.remove('show');
  if (confirmModalResolve) {
    confirmModalResolve(result);
    confirmModalResolve = null;
  }
}

// Handle ESC key for modals
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape') {
    // Close alert modal
    if (document.getElementById('alertModal').classList.contains('show')) {
      closeAlertModal();
    }
    // Close confirm modal (default to false/cancel)
    if (document.getElementById('confirmModal').classList.contains('show')) {
      closeConfirmModal(false);
    }
  }
});

// Handle Enter key for modals
document.addEventListener('keydown', (e) => {
  if (e.key === 'Enter') {
    // Confirm alert modal
    if (document.getElementById('alertModal').classList.contains('show')) {
      closeAlertModal();
    }
    // Confirm confirm modal (default to true/ok)
    if (document.getElementById('confirmModal').classList.contains('show')) {
      closeConfirmModal(true);
    }
  }
});

// Close modals when clicking outside
document.addEventListener('click', (e) => {
  // Alert modal
  if (e.target.id === 'alertModal') {
    closeAlertModal();
  }
  // Confirm modal
  if (e.target.id === 'confirmModal') {
    closeConfirmModal(false);
  }
  // History modal
  if (e.target.id === 'historyModal') {
    closeHistoryModal();
  }
});

// Show position details modal with live updates
async function showPositionDetails(strategyName) {
  try {
    const response = await fetch(`/api/portfolio/positions/${strategyName}`);
    const data = await response.json();

    console.log('📊 Position details response:', data);

    if (data.success) {
      console.log('📊 Position data:', data.positions);
      displayPositionModal(strategyName, data.positions);
      // Start live updates for the modal
      startPositionModalUpdates(strategyName);
    } else {
      showAlert(`Failed to load position details: ${data.error}`, 'error');
    }
  } catch (error) {
    console.error('Position details error:', error);
    showAlert(`Failed to load position details: ${error.message}`, 'error');
  }
}

// Display position details in modal
function displayPositionModal(strategyName, positions) {
  try {
    const modal = document.getElementById('positionModal');
    const title = document.getElementById('positionModalTitle');
    const content = document.getElementById('positionModalContent');

    if (!modal || !title || !content) {
      console.error('Modal elements not found');
      return;
    }

    title.innerHTML = `
      ${strategyName.charAt(0).toUpperCase() + strategyName.slice(1)} Strategy Positions
      <span class="live-indicator" id="positionLiveIndicator">🟢 Live</span>
    `;

    if (!positions || positions.length === 0) {
      content.innerHTML = `
        <div class="no-positions">
          <p>No positions found for this strategy.</p>
        </div>
      `;
    } else {
      try {
        content.innerHTML = positions.map((position, index) => {
          console.log(`📊 Processing position ${index}:`, position);

          // Validate position object
          if (!position || typeof position !== 'object') {
            console.error(`Invalid position object at index ${index}:`, position);
            return '<div class="position-item"><p>Invalid position data</p></div>';
          }

          // Safely handle potentially undefined values
          const quantity = parseFloat(position.quantity) || 0;
          const averagePrice = parseFloat(position.averagePrice) || 0;
          const currentPrice = parseFloat(position.currentPrice) || 0;
          const currentValue = parseFloat(position.currentValue) || 0;
          const totalCost = parseFloat(position.totalCost) || 0;
          const unrealizedPnL = parseFloat(position.unrealizedPnL) || 0;
          const realizedPnL = parseFloat(position.realizedPnL) || 0;
          const totalPnL = parseFloat(position.totalPnL) || (unrealizedPnL + realizedPnL);
          const assetCode = position.assetCode || position.symbol?.split('-')[0] || 'UNKNOWN';

      return `
      <div class="position-item" data-symbol="${position.symbol}">
        <div class="position-header">
          <h4>${assetCode}</h4>
          <span class="position-symbol">${position.symbol}</span>
        </div>
        <div class="position-details-grid">
          <div class="detail-item">
            <label>Quantity:</label>
            <span>${quantity.toFixed(8)} ${assetCode}</span>
          </div>
          <div class="detail-item">
            <label>Average Price:</label>
            <span>$${averagePrice.toFixed(2)}</span>
          </div>
          <div class="detail-item">
            <label>Current Price:</label>
            <span class="current-price" data-symbol="${position.symbol}">$${currentPrice.toFixed(2)}</span>
          </div>
          <div class="detail-item">
            <label>Total Cost:</label>
            <span>$${totalCost.toFixed(2)}</span>
          </div>
          <div class="detail-item">
            <label>Current Value:</label>
            <span class="current-value" data-symbol="${position.symbol}">$${currentValue.toFixed(2)}</span>
          </div>
          <div class="detail-item">
            <label>Unrealized P&L:</label>
            <span class="unrealized-pnl ${unrealizedPnL >= 0 ? 'positive' : 'negative'}" data-symbol="${position.symbol}">
              ${unrealizedPnL >= 0 ? '+' : ''}$${unrealizedPnL.toFixed(2)}
            </span>
          </div>
          <div class="detail-item">
            <label>Realized P&L:</label>
            <span class="${realizedPnL >= 0 ? 'positive' : 'negative'}">
              ${realizedPnL >= 0 ? '+' : ''}$${realizedPnL.toFixed(2)}
            </span>
          </div>
          <div class="detail-item">
            <label>Total P&L:</label>
            <span class="total-pnl ${totalPnL >= 0 ? 'positive' : 'negative'}" data-symbol="${position.symbol}">
              ${totalPnL >= 0 ? '+' : ''}$${totalPnL.toFixed(2)}
            </span>
          </div>
          <div class="detail-item">
            <label>Stop Loss:</label>
            <span>${position.stopLoss && typeof position.stopLoss === 'number' ? `$${position.stopLoss.toFixed(2)}` : 'None'}</span>
          </div>
        </div>
      </div>
          `;
        }).join('');
      } catch (error) {
        console.error('Error rendering position data:', error);
        content.innerHTML = `
          <div class="no-positions">
            <p>Error displaying position data: ${error.message}</p>
            <p>Please try refreshing the page.</p>
          </div>
        `;
      }
    }

    modal.classList.add('show');

    // Add click-outside-to-close functionality
    modal.onclick = function(event) {
      if (event.target === modal) {
        closePositionModal();
      }
    };

    // Add ESC key to close functionality
    document.addEventListener('keydown', handleModalKeydown);

  } catch (error) {
    console.error('Error in displayPositionModal:', error);
    showAlert(`Error displaying position modal: ${error.message}`, 'Error', 'error');
  }
}

// Handle keyboard events for modal
function handleModalKeydown(event) {
  if (event.key === 'Escape') {
    closePositionModal();
  }
}

// Close position details modal
function closePositionModal() {
  const modal = document.getElementById('positionModal');
  modal.classList.remove('show');
  modal.onclick = null; // Remove click handler
  document.removeEventListener('keydown', handleModalKeydown); // Remove keyboard handler

  // Stop live updates
  stopPositionModalUpdates();
}

// Status rotation system for multi-currency strategies
let statusRotationIntervals = new Map();

function startStatusRotation(strategyId, symbolStatuses) {
  // Clear existing rotation for this strategy
  if (statusRotationIntervals.has(strategyId)) {
    clearInterval(statusRotationIntervals.get(strategyId));
  }

  // Only rotate if there are multiple currencies
  const symbols = Object.keys(symbolStatuses);
  if (symbols.length <= 1) return;

  let currentIndex = 0;

  const rotateStatus = () => {
    const symbol = symbols[currentIndex];
    const symbolStatus = symbolStatuses[symbol];
    const assetCode = symbolStatus.assetCode;

    // Find the strategy card and update its status
    const strategyCard = document.querySelector(`[data-strategy-id="${strategyId}"]`);
    if (strategyCard) {
      const statusElement = strategyCard.querySelector('.status-reason');
      if (statusElement) {
        const statusText = `${assetCode}: ${symbolStatus.lastDecisionReason || 'Monitoring market conditions'}`;
        statusElement.textContent = statusText;

        // Add visual indicator for rotation
        statusElement.style.opacity = '0.7';
        setTimeout(() => {
          if (statusElement) statusElement.style.opacity = '1';
        }, 200);
      }
    }

    currentIndex = (currentIndex + 1) % symbols.length;
  };

  // Start rotation every 3 seconds
  const intervalId = setInterval(rotateStatus, 3000);
  statusRotationIntervals.set(strategyId, intervalId);

  // Run first rotation immediately
  rotateStatus();
}

function stopAllStatusRotations() {
  statusRotationIntervals.forEach((intervalId) => {
    clearInterval(intervalId);
  });
  statusRotationIntervals.clear();
}

// Position modal live updates
let positionModalUpdateInterval = null;
let currentModalStrategy = null;

// Start live updates for position modal
function startPositionModalUpdates(strategyName) {
  currentModalStrategy = strategyName;

  // Clear any existing interval
  if (positionModalUpdateInterval) {
    clearInterval(positionModalUpdateInterval);
  }

  // Update every 5 seconds for live data
  positionModalUpdateInterval = setInterval(async () => {
    await updatePositionModalData(strategyName);
  }, 5000);

  console.log(`🔄 Started live updates for ${strategyName} position modal`);
}

// Stop live updates for position modal
function stopPositionModalUpdates() {
  if (positionModalUpdateInterval) {
    clearInterval(positionModalUpdateInterval);
    positionModalUpdateInterval = null;
  }
  currentModalStrategy = null;
  console.log('⏹️ Stopped position modal live updates');
}

// Update position modal with fresh data
async function updatePositionModalData(strategyName) {
  try {
    // Check if modal is still open
    const modal = document.getElementById('positionModal');
    if (!modal || !modal.classList.contains('show')) {
      stopPositionModalUpdates();
      return;
    }

    // Fetch fresh position data
    const response = await fetch(`/api/portfolio/positions/${strategyName}`);
    const data = await response.json();

    if (data.success && data.positions) {
      // Update live indicator
      const liveIndicator = document.getElementById('positionLiveIndicator');
      if (liveIndicator) {
        liveIndicator.textContent = '🟢 Live';
        liveIndicator.style.color = '#10b981';

        // Flash the indicator to show update
        setTimeout(() => {
          liveIndicator.style.color = '#6b7280';
        }, 1000);
      }

      // Update each position's live data
      data.positions.forEach(position => {
        updatePositionElements(position);
      });

    } else {
      // Show error in live indicator
      const liveIndicator = document.getElementById('positionLiveIndicator');
      if (liveIndicator) {
        liveIndicator.textContent = '🔴 Error';
        liveIndicator.style.color = '#ef4444';
      }
    }
  } catch (error) {
    console.error('Error updating position modal:', error);

    // Show error in live indicator
    const liveIndicator = document.getElementById('positionLiveIndicator');
    if (liveIndicator) {
      liveIndicator.textContent = '🔴 Error';
      liveIndicator.style.color = '#ef4444';
    }
  }
}

// Update individual position elements with new data
function updatePositionElements(position) {
  const symbol = position.symbol;

  // Update current price
  const priceElement = document.querySelector(`.current-price[data-symbol="${symbol}"]`);
  if (priceElement) {
    const oldPrice = parseFloat(priceElement.textContent.replace('$', ''));
    const newPrice = position.currentPrice;

    priceElement.textContent = `$${newPrice.toFixed(2)}`;

    // Add price change animation
    if (newPrice > oldPrice) {
      priceElement.style.color = '#10b981'; // Green for up
    } else if (newPrice < oldPrice) {
      priceElement.style.color = '#ef4444'; // Red for down
    }

    // Reset color after animation
    setTimeout(() => {
      priceElement.style.color = '';
    }, 2000);
  }

  // Update current value
  const valueElement = document.querySelector(`.current-value[data-symbol="${symbol}"]`);
  if (valueElement) {
    valueElement.textContent = `$${position.currentValue.toFixed(2)}`;
  }

  // Update unrealized P&L
  const unrealizedElement = document.querySelector(`.unrealized-pnl[data-symbol="${symbol}"]`);
  if (unrealizedElement) {
    unrealizedElement.textContent = `${position.unrealizedPnL >= 0 ? '+' : ''}$${position.unrealizedPnL.toFixed(2)}`;
    unrealizedElement.className = `unrealized-pnl ${position.unrealizedPnL >= 0 ? 'positive' : 'negative'}`;
  }

  // Update total P&L
  const totalElement = document.querySelector(`.total-pnl[data-symbol="${symbol}"]`);
  if (totalElement) {
    totalElement.textContent = `${position.totalPnL >= 0 ? '+' : ''}$${position.totalPnL.toFixed(2)}`;
    totalElement.className = `total-pnl ${position.totalPnL >= 0 ? 'positive' : 'negative'}`;
  }
}

// Update position modal with real-time price data from WebSocket
function updatePositionModalPrices(symbolPrices) {
  if (!currentModalStrategy || !symbolPrices) return;

  // Check if modal is still open
  const modal = document.getElementById('positionModal');
  if (!modal || !modal.classList.contains('show')) {
    return;
  }

  // Update live indicator to show real-time updates
  const liveIndicator = document.getElementById('positionLiveIndicator');
  if (liveIndicator) {
    liveIndicator.textContent = '🟢 Live';
    liveIndicator.style.color = '#10b981';
  }

  // Update prices for each symbol in the modal
  Object.entries(symbolPrices).forEach(([symbol, priceData]) => {
    const newPrice = parseFloat(priceData.price || priceData.ask || 0);
    if (newPrice <= 0) return;

    // Update current price display
    const priceElement = document.querySelector(`.current-price[data-symbol="${symbol}"]`);
    if (priceElement) {
      const oldPrice = parseFloat(priceElement.textContent.replace('$', ''));
      priceElement.textContent = `$${newPrice.toFixed(2)}`;

      // Add price change animation
      if (newPrice > oldPrice) {
        priceElement.style.color = '#10b981'; // Green for up
      } else if (newPrice < oldPrice) {
        priceElement.style.color = '#ef4444'; // Red for down
      }

      // Reset color after animation
      setTimeout(() => {
        priceElement.style.color = '';
      }, 2000);

      // Update derived values (current value, unrealized P&L, total P&L)
      updateDerivedValues(symbol, newPrice);
    }
  });
}

// Update derived values when price changes
function updateDerivedValues(symbol, newPrice) {
  // Find the position item for this symbol
  const positionItem = document.querySelector(`.position-item[data-symbol="${symbol}"]`);
  if (!positionItem) return;

  // Get quantity from the display (parse from text)
  const quantityElement = positionItem.querySelector('.detail-item span');
  if (!quantityElement) return;

  const quantityText = quantityElement.textContent;
  const quantity = parseFloat(quantityText.split(' ')[0]);
  if (isNaN(quantity)) return;

  // Calculate new current value
  const newCurrentValue = quantity * newPrice;

  // Update current value display
  const valueElement = document.querySelector(`.current-value[data-symbol="${symbol}"]`);
  if (valueElement) {
    valueElement.textContent = `$${newCurrentValue.toFixed(2)}`;
  }

  // Get total cost to calculate unrealized P&L
  const costElements = positionItem.querySelectorAll('.detail-item span');
  let totalCost = 0;
  let realizedPnL = 0;

  costElements.forEach(element => {
    const parent = element.closest('.detail-item');
    if (parent && parent.querySelector('label').textContent === 'Total Cost:') {
      totalCost = parseFloat(element.textContent.replace('$', ''));
    }
    if (parent && parent.querySelector('label').textContent === 'Realized P&L:') {
      const text = element.textContent.replace(/[\$\+]/g, '');
      realizedPnL = parseFloat(text);
    }
  });

  if (totalCost > 0) {
    const unrealizedPnL = newCurrentValue - totalCost;
    const totalPnL = unrealizedPnL + realizedPnL;

    // Update unrealized P&L
    const unrealizedElement = document.querySelector(`.unrealized-pnl[data-symbol="${symbol}"]`);
    if (unrealizedElement) {
      unrealizedElement.textContent = `${unrealizedPnL >= 0 ? '+' : ''}$${unrealizedPnL.toFixed(2)}`;
      unrealizedElement.className = `unrealized-pnl ${unrealizedPnL >= 0 ? 'positive' : 'negative'}`;
    }

    // Update total P&L
    const totalElement = document.querySelector(`.total-pnl[data-symbol="${symbol}"]`);
    if (totalElement) {
      totalElement.textContent = `${totalPnL >= 0 ? '+' : ''}$${totalPnL.toFixed(2)}`;
      totalElement.className = `total-pnl ${totalPnL >= 0 ? 'positive' : 'negative'}`;
    }
  }
}

// Trade History Modal Functions
async function openTradeHistory(strategyName) {
  try {
    // Fetch trade history data
    const response = await fetch('/api/bot-data');
    const data = await response.json();

    if (!data.success) {
      showAlert(`Failed to load trade history: ${data.error}`, 'Error', 'error');
      return;
    }

    const tradeHistory = data.data.tradeHistory?.trades || [];

    // Filter trades for this strategy
    const strategyTrades = tradeHistory.filter(trade => trade.strategy === strategyName);

    // Display the modal
    displayTradeHistoryModal(strategyName, strategyTrades);

  } catch (error) {
    console.error('Trade history error:', error);
    showAlert(`Failed to load trade history: ${error.message}`, 'Error', 'error');
  }
}

function displayTradeHistoryModal(strategyName, trades) {
  const modal = document.getElementById('historyModal');
  const title = document.getElementById('historyModalTitle');
  const content = document.getElementById('historyModalContent');

  if (!modal || !title || !content) {
    console.error('History modal elements not found');
    return;
  }

  // Set title
  title.textContent = `${strategyName.charAt(0).toUpperCase() + strategyName.slice(1)} Trade History`;

  if (trades.length === 0) {
    content.innerHTML = `
      <div class="no-trades">
        <p>No trades found for this strategy.</p>
      </div>
    `;
  } else {
    // Calculate summary statistics
    const stats = calculateTradeStats(trades);

    // Generate content
    content.innerHTML = `
      <div class="history-summary">
        <div class="history-stat">
          <div class="history-stat-label">Total Trades</div>
          <div class="history-stat-value">${trades.length}</div>
        </div>
        <div class="history-stat">
          <div class="history-stat-label">Buy Orders</div>
          <div class="history-stat-value">${stats.buyCount}</div>
        </div>
        <div class="history-stat">
          <div class="history-stat-label">Sell Orders</div>
          <div class="history-stat-value">${stats.sellCount}</div>
        </div>
        <div class="history-stat">
          <div class="history-stat-label">Total Volume</div>
          <div class="history-stat-value">$${stats.totalVolume.toFixed(2)}</div>
        </div>
        <div class="history-stat">
          <div class="history-stat-label">Avg Trade Size</div>
          <div class="history-stat-value">$${stats.avgTradeSize.toFixed(2)}</div>
        </div>
        <div class="history-stat">
          <div class="history-stat-label">Date Range</div>
          <div class="history-stat-value">${stats.dateRange}</div>
        </div>
      </div>

      <table class="history-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Symbol</th>
            <th>Quantity</th>
            <th>Price</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          ${trades.map(trade => `
            <tr>
              <td>${formatTradeDate(trade.timestamp)}</td>
              <td><span class="trade-type-${trade.type}">${trade.type}</span></td>
              <td>${trade.symbol}</td>
              <td>${formatQuantity(trade.quantity)}</td>
              <td>$${formatPrice(trade.price)}</td>
              <td>$${trade.value.toFixed(2)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;
  }

  // Show modal
  modal.classList.add('show');
}

function calculateTradeStats(trades) {
  const buyTrades = trades.filter(t => t.type === 'buy');
  const sellTrades = trades.filter(t => t.type === 'sell');
  const totalVolume = trades.reduce((sum, trade) => sum + trade.value, 0);
  const avgTradeSize = totalVolume / trades.length;

  // Get date range
  const timestamps = trades.map(t => t.timestamp).sort((a, b) => a - b);
  const firstDate = new Date(timestamps[0]).toLocaleDateString();
  const lastDate = new Date(timestamps[timestamps.length - 1]).toLocaleDateString();
  const dateRange = timestamps.length > 1 ? `${firstDate} - ${lastDate}` : firstDate;

  return {
    buyCount: buyTrades.length,
    sellCount: sellTrades.length,
    totalVolume,
    avgTradeSize,
    dateRange
  };
}

function formatTradeDate(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
}

function formatQuantity(quantity) {
  if (quantity >= 1) {
    return quantity.toFixed(4);
  } else {
    return quantity.toFixed(8);
  }
}

function formatPrice(price) {
  if (price >= 1000) {
    return price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  } else if (price >= 1) {
    return price.toFixed(4);
  } else {
    return price.toFixed(8);
  }
}

function closeHistoryModal() {
  document.getElementById('historyModal').classList.remove('show');
}
